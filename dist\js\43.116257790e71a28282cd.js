"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[43],{7224:function(n,e,t){t.d(e,{z:function(){return N}});var i=t(13274),r=t(61643),o=t(23825);function a(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function l(){var n=a(["\n      width:100%;\n      height:100vh;\n    "]);return l=function(){return n},n}function c(){var n=a(["\n      position: absolute;\n      top: 0px;\n      left: 0; \n      right: 0;\n      bottom: 0;\n      overflow: hidden;\n    "]);return c=function(){return n},n}function s(){var n=a(["\n      position:absolute;\n      top:0;\n      left:0;\n      width:100%;\n      height:100%;\n      z-index:-1;\n    "]);return s=function(){return n},n}function u(){var n=a(["\n        position: absolute;\n        left:-100px;\n        top: -100px;\n        background-color: #EAEAEB;\n        width : calc(100% + 200px);\n        height : calc(100% + 200px);\n        overflow: hidden;\n        .canvas {\n          position : absolute;\n          left: 0px;\n          top: 0px;\n          &.canvas_drawing {\n            cursor : url(./static/icons/cursor_drawing.png) 8 8,auto;\n          }\n          &.canvas_moving {\n            cursor : url(./static/icons/cursor_moving.png), auto;\n          }\n          &.canvas_leftmove {\n            cursor : url(./static/icons/cursor_leftmove.png) 16 16,auto;\n          }\n          &.canvas_rightmove {\n            cursor : url(./static/icons/cursor_rightmove.png) 16 16,auto;\n          }\n          &.canvas_acrossmove {\n            cursor : url(./static/icons/cursor_acrossmove.png) 16 16,auto;\n          }\n          &.canvas_verticalmove {\n            cursor : url(./static/icons/cursor_verticalmove.png) 16 16,auto;\n          }\n          &.canvas_text {\n            cursor : text;\n          }\n          &.canvas_pointer {\n            cursor : pointer;\n          }\n          &.canvas_splitWall {\n            cursor : url(./static/icons/split.png) 0 0,auto;\n          }\n        }\n\n        .canvas_btns {\n          width: auto;\n          margin: 0 auto;\n          position: fixed;\n          display: flex;\n          justify-content: center;\n          bottom: 35px;\n          z-index:10;\n          left: 50%;\n          transform: translateX(-50%);\n          .btn {\n            ","\n            border-radius: 6px;\n            border: none;\n\n            font-weight: 600;\n            margin-right: 10px;\n            margin-left: 10px;\n          }\n          .design_btn {\n            background: #e6e6e6;\n            margin-right: 20px;\n          }\n          @media screen and (max-height: 600px){\n            bottom: 50px !important;\n          }\n    }\n    "]);return u=function(){return n},n}function d(){var n=a(["\n      position:absolute;\n      top:0px;\n      width:100%;\n      height:50px;\n      border-bottom:1px solid #eee;\n      background:#fff;\n      z-index:5;\n    "]);return d=function(){return n},n}function f(){var n=a(["\n      position:absolute;\n      z-index:2;\n      padding-left:2px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n      float:left;\n    "]);return f=function(){return n},n}function p(){var n=a(["\n      position:absolute;\n      right:0;\n      z-index:2;\n      padding-right:10px;\n      font-size:14px;\n      line-height:50px;\n      color:#333;\n    "]);return p=function(){return n},n}function h(){var n=a(["\n      width:100%;\n      font-size:16px;\n      line-height:50px;\n      text-align:center;\n    "]);return h=function(){return n},n}var m=(0,t(81639).rU)((function(n){var e=n.css;return{root:e(l()),content:e(c()),canvas3d:e(s()),canvas_pannel:e(u(),(0,o.fZ)()?"\n              width: 120px;\n              height: 36px;\n              font-size: 14px;\n            ":"\n              width: 200px;\n              height: 48px;\n              font-size: 16px;\n            "),navigation:e(d()),backBtn:e(f()),forwardBtn:e(p()),schemeNameSpan:e(h())}})),x=t(15696),g=t(41594),v=t(27347),b=t(2021),y=t(70524),w=t(88934),j=t(78154),S=t(78644),_=t(83657),k=t(99030),C=t(32184);function I(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function A(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return I(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return I(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var N=function(n){return n.showLight3DViewer="showLight3DViewer",n}({});(0,x.observer)((function(){var n=(0,r.B)().t,e=m().styles,t=A((0,g.useState)(""),2),a=(t[0],t[1]),l=A((0,g.useState)(-2),2),c=l[0],s=l[1],u=(0,y.P)();(0,g.useRef)(null);v.nb.UseApp(b.e.AppName),v.nb.instance&&(v.nb.t=n);var d=function(){v.nb.instance&&(v.nb.instance.bindCanvas(document.getElementById("cad_canvas")),v.nb.instance.update())};return(0,g.useEffect)((function(){v.nb.instance&&(v.nb.instance._is_website_debug=o.iG),window.addEventListener("resize",d),d(),v.nb.instance&&(v.nb.instance.initialized||(v.nb.instance.init(),v.nb.RunCommand(b.f.AiCadMode),v.nb.instance.layout_graph_solver._is_query_server_model_rooms=!1,v.nb.instance.layout_container.drawing_figure_mode=C.qB.Texture,v.nb.instance.prepare().then((function(){})),v.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),v.nb.instance.update()),v.nb.on_M("showLight3DViewer","LightMain",(function(n){n?(s(2),v.nb.emit(k.r.UpdateScene3D,!1)):s(-1)})),v.nb.on(w.U.LayoutSchemeOpened,(function(n){a(n.name),v.nb.emit(_.$T,_.Kw.Default)}))}),[]),(0,i.jsxs)("div",{className:e.root,children:[(0,i.jsx)(_.Ay,{}),(0,i.jsx)(j.A,{}),(0,i.jsxs)("div",{id:"Canvascontent",className:e.content,children:[(0,i.jsx)("div",{className:"3d_container "+e.canvas3d,style:{zIndex:c},children:(0,i.jsx)(S.A,{defaultViewMode:4})}),(0,i.jsx)("div",{id:"body_container",className:e.canvas_pannel,children:(0,i.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){u.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){u.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,i=Math.sqrt(e*e+t*t);u.homeStore.setInitialDistance(i/u.homeStore.scale)}},onTouchMove:function(n){if(2===n.touches.length){var e=n.touches[0].clientX-n.touches[1].clientX,t=n.touches[0].clientY-n.touches[1].clientY,i=Math.sqrt(e*e+t*t)/u.homeStore.initialDistance;i>5?i=5:i<.05&&(i=.05),u.homeStore.setScale(i),v.nb.DispatchEvent(v.n0.scale,i)}},onTouchEnd:function(){u.homeStore.setInitialDistance(null)}})})]})]})}))},7474:function(n,e,t){var i=t(81639);function r(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function o(){var n=r(["\n      position: fixed;\n      left: 0;\n      bottom: 15px;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(0, 0, 0, 0); /* 透明蒙层 */\n      display: flex;\n      justify-content: center;\n      align-items: flex-end; /* 从底部对齐 */\n      transition: transform 0.3s ease;\n      transform: translateY(100%); /* 初始状态在视口外 */\n      &.show {\n          transform: translateY(0); /* 显示状态 */\n      }\n      @media screen and (orientation: landscape) {\n        top: 0px;\n        bottom: auto;\n        right: 0px;\n        left: auto;\n        max-height: calc(var(--vh, 1vh) * 100);\n        transform: translateX(100%);\n        justify-content: flex-end;\n        &.show {\n          transform: translateX(0);\n        }\n\n      }\n    "]);return o=function(){return n},n}function a(){var n=r(["\n      background-color: white;\n      width: 90%;\n      padding: 20px;\n      border-radius: 8px;\n      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\n      @media screen and (orientation: landscape) {\n        height: 100%;\n        width: 100%;\n        max-width: 225px;\n        &.leftSizeEditor {\n          position:absolute;\n          left:0;\n          top:0;\n        }\n      }\n    "]);return a=function(){return n},n}function l(){var n=r(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: semibold;\n      font-size: 20px;\n      line-height: 1.4;\n      font-weight: 600;\n      margin-bottom: 25px;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      @media screen and (orientation: landscape) {\n        font-size: 16px;\n        margin-bottom: 35px;\n\n      }\n    "]);return l=function(){return n},n}function c(){var n=r(["\n      display:none;\n      font-size:12px;\n      line-height:20px;\n      font-weight:500;\n      margin-left:30px;\n      input[type='radio'], input[type='checkbox'] {\n        box-sizing: border-box;\n        padding: 0;\n      }\n      span {\n        position:relative;\n        bottom:2px;\n      }\n      @media screen and (orientation: landscape) {\n        display:inline;\n\n\n      }\n    "]);return c=function(){return n},n}function s(){var n=r(["\n      border-radius: 12px;\n      border: 1px solid #00000026;\n      padding: 10px 20px;\n      background-color: white;\n      color: #282828;\n      width: 92px;\n      height: 24px;\n      font-size: 12px;\n      align-items: center;\n      display: flex;\n      justify-content: center;\n      @media screen and (orientation: landscape) {\n        width: 80px;\n        height: 20px;\n        font-size: 12px;\n        padding: 3px 3px;\n        position:absolute;\n        left : 122px;\n        top : 50px;\n        margin-bottom:10px;\n\n      }\n    "]);return s=function(){return n},n}function u(){var n=r(["\n      display: flex;\n      align-items: center;\n      margin-bottom: 20px;\n      justify-content: space-between;\n    "]);return u=function(){return n},n}function d(){var n=r(["\n        flex: 1;\n        margin-right: 20px;\n        margin-left: 20px;\n        @media screen and (orientation: landscape) {\n          margin-right: 0px;\n          margin-left: 0px;\n        }\n    "]);return d=function(){return n},n}function f(){var n=r(["\n        width: 124px;\n        @media screen and (orientation: landscape) {\n          width: 80px;\n          font-size : 12px;\n        }\n    "]);return f=function(){return n},n}e.A=(0,i.rU)((function(n){var e=n.css;return{root:e(o()),container:e(a()),title:e(l()),geometricCheck:e(c()),resetBtn:e(s()),sliderContainer:e(u()),slider:e(d()),input:e(f())}}))},23184:function(n,e,t){t.d(e,{A:function(){return K}});var i=t(13274),r=t(41594);function o(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function a(){var n=o(["\n      display: flex;\n      position: fixed;\n      top: calc(var(--vh, 1vh) * 50);\n      transform: translateY(-50%);\n      right: 12px;\n      flex-direction: column;\n      z-index: 11;\n      padding: 20px 0px;\n      gap: 12px;\n      background-color: #fff;\n      border-radius: 30px;\n      /* position: relative; */\n    "]);return a=function(){return n},n}function l(){var n=o(["\n      background-color: rgba(0, 0, 0, 0.40) !important;\n      backdrop-filter: blur(50px) !important;\n      color: #fff !important;\n      .iconButtonText\n      {\n        color: #fff !important;\n      }\n      .icon\n      {\n        color: #fff !important;\n      }\n    "]);return l=function(){return n},n}function c(){var n=o(["\n      display: flex;\n      position: absolute;\n      bottom: 0px;\n      right: 62px;\n      flex-direction: column;\n      z-index: 11;\n      padding: 20px 0px;\n      gap: 12px;\n      background-color: #fff;\n      border-radius: 30px;\n    "]);return c=function(){return n},n}function s(){var n=o(["\n      position : fixed;\n      top : 40px;\n      left : 40px;\n      right : 40px;\n      bottom : 40px;\n      z-index:101\n    "]);return s=function(){return n},n}function u(){var n=o(["\n      width: 48px;\n      height: auto;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 30px;\n      flex-direction: column;\n      position: relative;\n      /* margin-bottom: 12px; */\n      .iconButtonText\n      {\n        font-size: 12px;\n        color: #282828;\n        margin-top: 4px;\n      }\n      .iconLabel {\n        font-size: 13px;\n        position: absolute;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n      .divider\n      {\n        margin-top: 12px;\n        width: 100%;\n        height: 1px;\n        background-color: #E0E0E0;\n      }\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 28px !important;\n        height: 28px !important;\n        font-size: 16px !important;\n      }     \n      \n      @media screen and (orientation: portrait) {\n        width: 48px;\n        height: 48px;\n      }\n\n      // 横屏样式\n      @media screen and (orientation: landscape) {\n        width: 48px;\n        /* min-height: 40px; */\n      }\n      \n      @keyframes flashEffect {\n        0% {\n          background-color: rgba(255, 255, 255, 0.7); /* 原始背景色 */\n        }\n        50% {\n          background-color: rgba(200, 200, 255, 0.7); /* 高亮颜色 */\n        }\n        100% {\n          background-color: rgba(255, 255, 255, 0.7); /* 回到原始背景色 */\n        }\n      }\n    "]);return u=function(){return n},n}function d(){var n=o(["\n      font-size: 12px;\n      color: #000;\n      margin-top: 4px;\n    "]);return d=function(){return n},n}function f(){var n=o(["\n      position: fixed;\n      background: #fff;\n      z-index: 20;\n      right: 64px;\n      top: 50vh;\n      transform: translateY(-50%);\n      font-size:15px;\n      padding: 12px;\n      line-height: 28px;\n      border-radius: 8px;\n    "]);return f=function(){return n},n}function p(){var n=o(["\n      position: fixed;\n      background-color: #ffffff;\n      top: 50%;\n      right: 60px;\n      border-radius: 8px;\n      width: 210px;\n      font-size: 13px;\n      .camera_container {\n        padding: 8px;\n        .content {\n          display: flex;\n          align-items: center;\n          gap: 8px;\n          .slider {\n            flex: 1;\n          }\n          .slider-camera {\n            flex: 1;\n            padding-bottom: 5px;\n            .camera-state {\n              display: flex;\n              justify-content: space-between;\n            }\n          }\n        }\n      }\n    "]);return p=function(){return n},n}function h(){var n=o(["\n      position: fixed;\n      background-color: #ffffff;\n      bottom: 100px;\n      right: 60px;\n      width: 300px;\n      border-radius: 10px;\n      z-index: 10;\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 5px;\n      padding: 5px;\n      .viewGrid {\n        border-radius: 10px;\n        overflow: hidden;\n      }\n    "]);return h=function(){return n},n}function m(){var n=o(["\n      background-color: #ffffff;\n      position: fixed;\n      bottom: 100px;\n      right: 60px;\n      width: 300px;\n      border-radius: 10px;\n      z-index: 11;\n      display: grid;\n      grid-template-columns: repeat(2, 1fr);\n      gap: 5px;\n      padding: 5px;\n      .loading-item {\n        border-radius: 10px;\n        position:relative;\n        overflow: hidden;\n        display: flex; /* 使用flex布局 */\n        flex-direction: column; /* 垂直排列 */\n        justify-content: center; /* 水平居中 */\n        align-items: center; /* 垂直居中 */\n        gap: 8px;\n        height: 106.9px; /* 保持高度 */\n        width: 142.5px; /* 保持宽度 */\n        span {\n          font-size: 12px;\n          color: #C0C0C0;\n        }\n      }\n    "]);return m=function(){return n},n}function x(){var n=o(["\n      display: flex;\n      gap: 10px;\n      position: fixed;\n      flex-direction: column;\n      top: 40%;\n      right: 65px;\n      .ratioBtn {\n        background-color:rgb(218, 218, 218);\n        height: 30px;\n        width: 45px;\n        border-radius: 5px;\n        font-size: 16px;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n      }\n    "]);return x=function(){return n},n}var g=(0,t(81639).rU)((function(n){var e=n.css;return{container:e(a()),blackColor:e(l()),morebtns_container:e(c()),center_container:e(s()),iconButton:e(u()),name:e(d()),checkBoxes:e(f()),camera:e(p()),viewCamera:e(h()),loading:e(m()),ratioContainer:e(x())}})),v=t(27347),b=t(88934),y=t(15696),w=t(70524),j=t(67869),S=t(23664),_=t(11180),k=t(19268),C=t(87248),I=t(62625),A=t(54282),N=t(68586);function z(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function E(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function O(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),i.forEach((function(e){E(n,e,t[e])}))}return n}function M(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return z(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return z(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var D={};D[A.uW.CadEzdxfDrawing]=!1,D[A.uW.CadRoomStrucure]=!0,D[A.uW.CadFurniture]=!0,D[A.uW.CadCabinet]=!0,D[A.uW.CadOutLine]=!0,D[A.uW.CadLighting]=!1,D[A.uW.CadCeiling]=!1,D[A.uW.CadDecorates]=!1,D[A.uW.CadSubRoomAreaDrawing]=!1,D[A.uW.CadDimensionWallElement]=!1,D[A.uW.CadDimensionOutterWallElement]=!1,D[A.uW.CadRoomName]=!0,D[A.uW.RulerDrawing]=!0;var P=function(n){var e=n.isVisible,t=g().styles,o=v.nb.t,a=(v.nb.instance.layout_container,M((0,r.useState)(D),2)),l=a[0],c=a[1],s=[{id:A.uW.CadRoomStrucure,title:o("墙体结构"),titleCn:"墙体结构",type:"checkbox",checked:l[A.uW.CadRoomStrucure]},{id:A.uW.CadFurniture,title:o("家具"),titleCn:"家具",type:"checkbox",checked:l[A.uW.CadFurniture]},{id:A.uW.CadCabinet,title:o("定制柜"),titleCn:"定制柜",type:"checkbox",checked:l[A.uW.CadCabinet]},{id:A.uW.CadSubRoomAreaDrawing,title:o("区域"),titleCn:"区域",type:"checkbox",checked:l[A.uW.CadSubRoomAreaDrawing]},{id:A.uW.CadCeiling,title:o("吊顶"),titleCn:"吊顶",type:"checkbox",checked:l[A.uW.CadCeiling]},{id:A.uW.CadRoomName,title:o("空间名称"),titleCn:"空间名称",type:"checkbox",checked:l[A.uW.CadRoomName]},{id:A.uW.RulerDrawing,title:o("量尺"),titleCn:"量尺",type:"checkbox",checked:l[A.uW.RulerDrawing]}];s=s.filter((function(n){return n}));return(0,r.useEffect)((function(){return v.nb.DispatchEvent(v.n0.HandleSwitchDrawingLayer,D),v.nb.on_M(b.U.SwitchDrawingLayer,"display-check-box",(function(n){var e=O({},l,n);c(e)})),function(){}}),[]),(0,i.jsx)("div",{className:t.checkBoxes,style:{display:e?"block":"none"},children:s.map((function(n,e){return(0,i.jsx)("div",{children:(0,i.jsx)(N.A,{checked:l[n.id],onChange:function(e){!function(n){if(void 0===l[n.id]&&(l[n.id]=!1),void 0!==l[n.id]){var e=O({},l);e[n.id]=!e[n.id],v.nb.DispatchEvent(v.n0.HandleSwitchDrawingLayer,e)}}(n)},children:n.title})},"display_check_"+e)}))})},F=t(17365),T=t(49063),L=t(10371),B=t(21491),U=t(23825),R=t(46396),W=t(43417),H=t(57189),V=t(65640);function $(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function Y(n,e,t,i,r,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(i,r)}function X(n){return function(){var e=this,t=arguments;return new Promise((function(i,r){var o=n.apply(e,t);function a(n){Y(o,i,r,a,l,"next",n)}function l(n){Y(o,i,r,a,l,"throw",n)}a(void 0)}))}}function q(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return $(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return $(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(n,e){var t,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,i&&(r=2&l[0]?i.return:l[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,l[1])).done)return r;switch(i=0,r&&(l=[2&l[0],r.value]),l[0]){case 0:case 1:r=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,i=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!r||l[1]>r[0]&&l[1]<r[3])){o.label=l[1];break}if(6===l[0]&&o.label<r[1]){o.label=r[1],r=l;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(l);break}r[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],i=0}finally{t=r=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var K=(0,y.observer)((function(n){var e=n.setSceneMode,t=g().styles,o=(0,w.P)(),a=o.homeStore,l=a.setShowDreamerPopup,c=a.setIsdrawPicture,s=v.nb.instance.scene3D,u=v.nb.instance.layout_container,d=q(C.A.useMessage(),2),f=d[0],p=d[1],h=v.nb.t,m=q((0,r.useState)(null),2),x=m[0],y=m[1],A=((0,T.Zp)(),q((0,r.useState)(!1),2)),N=A[0],z=A[1],E=[{iconType:"icon-chexiao",onClick:function(){v.nb.RunCommand(v._I.Undo)},name:h("撤销"),isHidden:"2D"!==o.homeStore.viewMode},{iconType:"icon-huifu",onClick:function(){v.nb.RunCommand(v._I.Redo)},name:h("恢复"),isHidden:"2D"!==o.homeStore.viewMode,divider:!0},{iconType:"icon-change_logo",onClick:function(){return X((function(){return G(this,(function(n){return"3D"==o.homeStore.viewMode?e("3D_FirstPerson"):e("3D"),[2]}))}))()},name:"3D"==o.homeStore.viewMode?h("漫游"):h("鸟瞰"),isHidden:"2D"==o.homeStore.viewMode||o.homeStore.isdrawPicture},{iconType:"icon-save",onClick:function(){0==u._room_entities.length?C.A.error(h("当前方案为空，无法保存！")):null==u._layout_scheme_id?o.homeStore.setShowSaveLayoutSchemeDialog({show:!0,source:"topMenu"}):v.nb.DispatchEvent(v.n0.SaveLayoutScheme,null)},name:h("保存"),isHidden:o.homeStore.isdrawPicture},{iconType:"icon-tuku",onClick:function(){0==u._room_entities.length?C.A.warning(h("请先创建方案")):(o.homeStore.setShowAtlas(!0),o.homeStore.setAtlasMode("aidraw"))},name:h("图册"),id:"renderingTukuBtnId"},{iconType:"icon-xiangjishezhi",onClick:function(){o.homeStore.setShowSubmitInfo(!0)},name:h("相机"),isHidden:!o.homeStore.isdrawPicture},{iconType:"icon-lishibanben",onClick:function(){v.nb.emit(b.U.setMultiSchemeListVisible,!0)},name:h("多方案"),isHidden:!0},{iconType:"icondisplay",onClick:function(){},name:h("显隐"),isHidden:"2D"!==o.homeStore.viewMode},{iconType:"iconhuizhong",onClick:function(){if("2D"===o.homeStore.viewMode)window.innerWidth<.8*window.innerHeight?F.f.focusCenterByWholeBox(u,.7):F.f.focusCenterByWholeBox(u,.5),v.nb.instance.update();else{u.updateWholeBox();var n=u._whole_bbox.getCenter(new j.Pq0);s.setCenter(n)}},name:h("居中"),isHidden:"2D"!==o.homeStore.viewMode},{iconType:"icon-baojia",onClick:function(){return X((function(){var n,e,t,i,r,o,a;return G(this,(function(l){switch(l.label){case 0:return{},e=v.nb.instance,t=S.K.instance,i={seriesKgId:null,ruleId:null,seedSchemeId:null,ruleName:null,seedSchemeName:null,roomName:null,seriesName:null,status:null,thumbnail:null,roomList:null,ruleImageList:null,layoutTemplates:null},(r=t.makeQuoteData(e.layout_container,new _._(i)))?(n={data:JSON.stringify(r)},[4,(0,k.fS)(n)]):[3,2];case 1:(o=l.sent()).success&&(C.A.success(h("报价成功")),(a=document.createElement("a")).href=o.data,document.body.appendChild(a),a.click(),document.body.removeChild(a)),l.label=2;case 2:return[2]}}))}))()},name:h("装修"),isHidden:!0},{iconType:"icon-baojia",onClick:function(){return X((function(){return G(this,(function(n){return o.homeStore.setShowCabinetCompute(!0),[2]}))}))()},name:h("算量"),isHidden:"2D"!==o.homeStore.viewMode},{iconType:"icon-search",onClick:function(){return X((function(){return G(this,(function(n){return l(!0),[2]}))}))()},name:h("找相似"),isHidden:"2D"!==o.homeStore.viewMode||!U.um},{iconType:(null==s?void 0:s.outlineMaterialMode)==L.Gf.WhiteModelOutline?"iconShowoutline_Nor":"iconShowmaterial_Nor",onClick:function(){var n=(null==s?void 0:s.outlineMaterialMode)==L.Gf.WhiteModelOutline;s.outlineMaterialMode=n?L.Gf.MaterialOnly:L.Gf.WhiteModelOutline},name:h("轮廓"),isHidden:"2D"===o.homeStore.viewMode,isChecked:!0},{iconType:v.nb.instance.Configs.isClickDrawPic?"icon-chanpinzhiru":"icon-maikefeng1",onClick:function(){v.nb.instance.Configs.isClickDrawPic=!v.nb.instance.Configs.isClickDrawPic,v.nb.emit_M(b.U.FigureElementSelected,null),v.nb.instance.scene3D.setSelectionBox(null),f.open({type:null,content:v.nb.instance.Configs.isClickDrawPic?h("进入演讲模式"):h("进入换搭模式"),className:"custom-class"})},name:v.nb.instance.Configs.isClickDrawPic?h("换搭"):h("演讲"),isHidden:"3D_FirstPerson"!==o.homeStore.viewMode||o.homeStore.isdrawPicture},{iconType:"icon-Frame",onClick:function(){v.nb.DispatchEvent(v.n0.autoSave,null),c(!0),v.nb.instance.Configs.isClickDrawPic=!0,s.raycasteControls.onSelectedFigure(null)},name:h("出图"),isHidden:"3D_FirstPerson"!==o.homeStore.viewMode||o.homeStore.isdrawPicture,isChecked:!0},{iconType:"icon-fenxiang",onClick:function(){return X((function(){return G(this,(function(n){return z(!N),[2]}))}))()},name:h("分享"),isHidden:o.homeStore.isdrawPicture},{iconType:"iconmore",onClick:function(){},name:null,isHidden:"2D"!==o.homeStore.viewMode,isChecked:!0},{iconType:"icon-icon",onClick:function(){c(!1),v.nb.instance.Configs.isClickDrawPic=!1,v.nb.instance.renderSubmitObject={drawPictureMode:null,radioMode:0,resolution:0},s.setLightMode(L.Ei.Day),H.p.instance.cleanLighting(),v.nb.instance.scene3D.setLightGroupVisible(!1,!1,!1),W.Y.cleanLight(),o.homeStore.setDrawPictureMode("aiDrawing")},name:h("取消"),isHidden:!o.homeStore.isdrawPicture,isChecked:!0}],O=q((0,r.useState)(),2),M=O[0],D=O[1];(0,r.useEffect)((function(){v.nb.on_M(b.U.Scene3DUpdated,"Statusbars",(function(){v.nb.instance.layout_container;s=v.nb.instance.scene3D}))}),[]),(0,r.useEffect)((function(){var n=function(n){null!==n.target.closest("#ipad-sideToolbar")||y(null)};return document.addEventListener("mousedown",n),function(){document.removeEventListener("mousedown",n)}}),[]);var $=[{iconType:"iconfile",onClick:function(){o.homeStore.setShowEnterPage({show:!0,source:"sideToolbar"})},name:h("新建")},{iconType:"iconbuzhisucai",onClick:function(){o.homeStore.setShowMySchemeList(!0)},name:h("方案")}];$=$.filter((function(n){return n&&!n.isHidden})),(0,r.useEffect)((function(){D(E.filter((function(n){return null!==n&&!n.isHidden}))),V.log("store.homeStore.viewMode",o.homeStore.viewMode)}),[o.homeStore.viewMode,o.homeStore.drawPictureMode,o.homeStore.isdrawPicture,v.nb.instance.Configs.isClickDrawPic]);var Y="2D"===o.homeStore.viewMode;return(0,i.jsxs)("div",{id:"ipad-sideToolbar",children:[(0,i.jsxs)("div",{className:"".concat(t.container," ").concat("2D"!==o.homeStore.viewMode?t.blackColor:""),children:[M&&M.map((function(n,e){return(0,i.jsx)(I.A,{title:n.name,placement:"left",children:(0,i.jsxs)("div",{className:"".concat(t.iconButton," ").concat(x===n.iconType?n.isChecked?"checked":"notChecked":""),onClick:function(){return e=n.iconType,t=n.onClick,y(x===e?null:e),void t();var e,t},id:n.id,children:[(0,i.jsx)("svg",{className:"icon","aria-hidden":"true",style:{width:"20px",height:"20px"},fill:"2D"!==o.homeStore.viewMode?"#fff":"#595959",children:(0,i.jsx)("use",{xlinkHref:"#".concat(n.iconType)})}),(0,i.jsx)(R.If,{condition:n.name,children:(0,i.jsx)("div",{className:"iconButtonText",children:n.name})}),(0,i.jsx)(R.If,{condition:n.divider,children:(0,i.jsx)("span",{className:"divider"})})]},e)},e)})),"iconmore"===x&&(0,i.jsx)("div",{className:t.morebtns_container,children:$.map((function(n,e){return(0,i.jsxs)("div",{className:t.iconButton,onClick:function(){y(null),n.onClick()},id:n.id,children:[(0,i.jsx)("svg",{className:"icon","aria-hidden":"true",style:{width:"20px",height:"20px"},fill:"2D"!==o.homeStore.viewMode?"#fff":"#595959",children:(0,i.jsx)("use",{xlinkHref:"#".concat(n.iconType)})}),(0,i.jsx)("div",{className:"iconButtonText",children:n.name}),(0,i.jsx)(R.If,{condition:n.divider,children:(0,i.jsx)("span",{className:"divider"})})]},e)}))})]}),(0,i.jsx)(P,{isVisible:"icondisplay"===x&&Y}),N&&(0,i.jsx)("div",{className:"",style:{position:"fixed",top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:"999",background:"#fff",padding:"10px",boxShadow:"0 2px 8px rgba(0,0,0,0.15)",borderRadius:"4px"},children:(0,i.jsx)(B.A,{onClose:function(){z(!1)}})}),p]})}))},50617:function(n,e,t){t.d(e,{A:function(){return hn}});var i=t(13274),r=t(41594),o=t(40706),a=t(92415);function l(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function c(){var n=l(["\n      background: #FFF;\n      height: 100%;\n      z-index: 999;\n      padding: 0 0 0 16px;\n      /* overflow: hidden; */\n      position: fixed;\n      top: 48px;\n      left: 0;\n    "]);return c=function(){return n},n}function s(){var n=l(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    "]);return s=function(){return n},n}function u(){var n=l(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: semibold;\n      font-size: 20px;\n      line-height: 1.4;\n      letter-spacing: 0px;\n      text-align: left;\n      font-weight: 600;\n      margin: 16px 0px;\n    "]);return u=function(){return n},n}function d(){var n=l(["\n      border-radius: 30px;\n      background: #F2F3F5;\n      color: #000;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 20px;\n      letter-spacing: 0px;\n      text-align: left;\n      min-width: 70%;\n      height: 32px;\n      border: none;\n      margin: 16px 0 0 0px;\n      padding-left: 30px;\n      :focus {\n        border-color: none; /* 取消聚焦边框 */\n        box-shadow: none; /* 取消聚焦阴影效果 */\n        outline: none; /* 取消聚焦时的外边框效果 */\n      }\n    "]);return d=function(){return n},n}function f(){var n=l(["\n      position: absolute;\n      top: 113px;\n      left: 7px;\n    "]);return f=function(){return n},n}function p(){var n=l(["\n      position: absolute;\n      top: 97px;\n      right: 38%;\n      cursor: pointer;\n    "]);return p=function(){return n},n}function h(){var n=l(["\n      width: 24%;\n      margin: 16px 8px 0px 0;\n      display: flex;\n      justify-items: baseline;\n      align-items: center;\n      a {\n        color: #ffffff;\n        padding: 5px;\n        line-height: 23px;\n        height: 34px;\n      }\n      a:hover {\n        color: #3D9EFF;\n        border-radius: 10px;\n        background: #BFD8FF14;\n        transition: all .3s;\n      }\n    "]);return h=function(){return n},n}function m(){var n=l(["\n      display: flex;\n      justify-content: space-between;\n      padding-right: 28px;\n      margin-bottom: 16px;\n    "]);return m=function(){return n},n}function x(){var n=l(["\n      height: auto;\n      @media screen and (orientation: landscape) {\n        padding-bottom: 16px;\n      }\n    "]);return x=function(){return n},n}function g(){var n=l(["\n      @media screen and (orientation: landscape) {\n        width: 224px;\n      }\n    "]);return g=function(){return n},n}function v(){var n=l(["\n      width: 180px;\n      height: 28px;\n      display: flex;\n      justify-content: center;\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: semibold;\n      font-size: 20px;\n      font-weight: 600;\n      color: #959598;\n      z-index: 9;\n      align-items: center;\n      width: 100%;\n      padding: 0px 16px;\n      margin: 16px 0px;\n      position: relative;\n      div{\n        margin-right: 8px;\n      }\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: auto;\n        font-size: 16px;\n        top: 16px;\n      }\n      @media screen and (orientation: landscape) {\n        font-size: 16px;\n        justify-content: space-between;\n      }\n      @media screen and (orientation: portrait) {\n        top : 0px;\n        left: 12px;\n        width: 50%;\n        justify-content: start;\n      }\n      .checked:after\n      {\n        content: '';\n        display: block;\n        width: 20px;\n        height: 3px;\n        border-radius: 10px;\n        background-color: #282828;\n        margin-top: 5px;\n        margin-left: 23px;\n        position: absolute;\n      }\n    "]);return v=function(){return n},n}function b(){var n=l(["\n      overflow-y: hidden;\n      width:100%;\n      display:flex;\n      overflow-x: auto;\n      height: 230px;\n      &::-webkit-scrollbar {\n        display: none;\n      }\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100 - 190px);\n        width: 100%;\n        display: block;\n        overflow-y: auto;\n      }\n      @media screen and (orientation: portrait) {\n        margin-top: 10px;\n      }\n    "]);return b=function(){return n},n}function y(){var n=l(["\n      margin-top: 50px;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100 - 80px) !important;\n      }\n    "]);return y=function(){return n},n}function w(){var n=l(["\n      float:left;\n      @media screen and (orientation: landscape) {\n        float: none;\n      }\n    "]);return w=function(){return n},n}function j(){var n=l(["\n      width: 270px;\n      height: 170px;\n      box-sizing: border-box;\n      position: relative;\n      margin:10px;\n      @media screen and (orientation: landscape) {\n        width: 100%;\n        margin: 0;\n        padding: 0 12px;\n        height: 137px;\n        margin-bottom: 40px;\n      }\n    "]);return j=function(){return n},n}function S(){var n=l(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin: 10px 0 4px 0;\n      @media screen and (orientation: landscape) {\n        margin-top: 4px;\n      }\n    "]);return S=function(){return n},n}function _(){var n=l(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: medium;\n      font-size: 14px;\n      line-height: 22px;\n      letter-spacing: 0px;\n      text-align: left;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      width: 78%;\n    "]);return _=function(){return n},n}function k(){var n=l(["\n      color: #5B5E60;\n      border-radius: 12px;\n      border: 1px solid #0000000F;\n      padding: 4px 8px;\n    "]);return k=function(){return n},n}function C(){var n=l(["\n      color: #5B5E60;\n      font-family: PingFang SC;\n      font-size: 12px;\n      letter-spacing: 0px;\n      text-align: left;\n      background-color: #F2F3F5;\n      width: auto;\n      border-radius: 2px;\n      padding: 2px 8px;\n      display: block;\n      white-space: nowrap;\n    "]);return C=function(){return n},n}function I(){var n=l(["\n      overflow-y: hidden !important;\n    "]);return I=function(){return n},n}function A(){var n=l(["\n      width: 100%;\n      height: 98vh;\n      overflow: auto;\n      position: absolute;\n      left: 0;\n      canvas {\n        margin-left:5px;\n        margin-top:5px;\n        cursor : pointer;\n      }\n    "]);return A=function(){return n},n}function N(){var n=l(["\n      height: 100%;\n      position: absolute;\n      right: 0;\n      top: 0;\n      width: 4px;\n      cursor: col-resize;\n      z-index: 998;\n    "]);return N=function(){return n},n}function z(){var n=l(["\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      height: 100%;\n      img\n      {\n        width: 120px;\n        height: 120px;\n      }\n      .desc\n      {\n        text-align: center;\n        margin-top: 10px;\n        color: #A2A2A5;\n        font-size: 12px;\n      }\n    "]);return z=function(){return n},n}function E(){var n=l(["\n      border-radius: 4px;\n      height: 100%;\n      overflow: hidden;\n      @media screen and (orientation: landscape) {\n      }\n      img {\n        transition: all .5s;\n        width: 100%;\n        border-radius: 4px;\n      }\n    "]);return E=function(){return n},n}function O(){var n=l(["\n      border: 2px solid #9242FB !important;\n    "]);return O=function(){return n},n}function M(){var n=l(["\n      position: absolute;\n      top: 4px;\n      right: 16px;\n      border-radius: 4px;\n      background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      color: #fff;\n      padding: 4px 8px;\n      border-radius: 4px;\n    "]);return M=function(){return n},n}function D(){var n=l(["\n      \n      \n    "]);return D=function(){return n},n}function P(){var n=l(["\n      width: 560px !important;\n      @media screen and (max-width: 450px) {\n        width: 300px !important;\n      }\n      @media screen and (orientation: landscape) {\n        width: 900px !important;\n      }\n    "]);return P=function(){return n},n}function F(){var n=l(["\n      padding: 20px;\n      @media screen and (max-width: 450px) {\n        padding: 14px;\n      }\n      @media screen and (orientation: landscape) {\n        display: flex;\n        .ant-carousel {\n          max-width: 420px;\n          margin: auto 0;\n          margin-right: 40px;\n        }\n      }\n    "]);return F=function(){return n},n}function T(){var n=l(["\n      .swj-baseComponent-Containersbox-title{\n        background-color: #fff !important;\n      }\n      .swj-baseComponent-Containersbox-body\n      {\n        > div:first-child {\n          height: 760px !important; /* 只影响第一个子 div */\n          @media screen and (max-width: 450px) {\n            height: 500px !important;\n          }\n          @media screen and (orientation: landscape) {\n            height: 475px !important;\n            margin-top: -1px;\n          }\n        }\n       \n      }\n      \n    "]);return T=function(){return n},n}function L(){var n=l(["\n      display: flex;\n      flex-wrap: wrap;\n      gap: 6px;\n      height: 760px;\n      overflow-y: auto;\n      max-height: 320px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 205px;\n        gap: 12px;\n      }\n      @media screen and (orientation: landscape) {\n        gap: 18px;\n      }\n      ::-webkit-scrollbar-thumb\n      {\n        display: none;\n      }\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n    "]);return L=function(){return n},n}function B(){var n=l(["\n      width: 100%;\n      \n      height: 290px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 300px;\n        height: 160px;\n      }\n      @media screen and (orientation: landscape) {\n        border-radius: 8px;\n        height: 320px;\n        min-width: 520px;\n      }\n    "]);return B=function(){return n},n}function U(){var n=l(["\n      display: flex;\n      justify-content: space-between;\n      margin-top: 10px;\n      @media screen and (orientation: landscape) {\n        margin-top: 20px;\n      }\n      button{\n        width: 50%;\n        height: 32px;\n        margin-right: 10px;\n      }\n      .leftBtn{\n        color: #000;\n        border-radius: 10px;\n        border: none;\n        border-radius: 6px;\n        background: #EAEAEB;\n      }\n      .rightBtn{\n        border-radius: 6px;\n        border: none;\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n        color: #fff;\n      }\n    "]);return U=function(){return n},n}var R=(0,t(81639).rU)((function(n){var e=n.css;return{container:e(c()),titleContainer:e(s()),title:e(u()),container_input:e(d()),Icon:e(f()),IconDelete:e(p()),selectInfo:e(h()),findInfo:e(m()),roomListBar:e(x()),bottomPanel:e(g()),topSelect:e(v()),container_listInfo:e(b()),type:e(y()),container_box:e(w()),container_data:e(j()),textInfo:e(S()),container_title:e(_()),container_desc:e(k()),seriesStyle:e(C()),noScroll:e(I()),side_list:e(A()),line:e(N()),emptyInfo:e(z()),Popover_hoverInfo:e(E()),Popover_hoverInfo_type:e(O()),tag_label:e(M()),applyBtn:e(D()),panel:e(P()),panelContent:e(F()),panelContainer:e(T()),materialList:e(L()),roomImg:e(B()),applyBtnInfo:e(U())}})),W=t(50060),H=t(61307),V=t(13915),$=t(70524),Y=t(27347),X=t(88934),q=t(83067),G=t(61643),K=t(23825),Z=t(15696),J=t(42751),Q=t(97082),nn=t(62460),en=t(46396),tn=t(65640);function rn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function on(n,e,t,i,r,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(i,r)}function an(n){return function(){var e=this,t=arguments;return new Promise((function(i,r){var o=n.apply(e,t);function a(n){on(o,i,r,a,l,"next",n)}function l(n){on(o,i,r,a,l,"throw",n)}a(void 0)}))}}function ln(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function cn(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),i.forEach((function(e){ln(n,e,t[e])}))}return n}function sn(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,i)}return t}(Object(e)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))})),n}function un(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||fn(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dn(n){return function(n){if(Array.isArray(n))return rn(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||fn(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fn(n,e){if(n){if("string"==typeof n)return rn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?rn(n,e):void 0}}function pn(n,e){var t,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,i&&(r=2&l[0]?i.return:l[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,l[1])).done)return r;switch(i=0,r&&(l=[2&l[0],r.value]),l[0]){case 0:case 1:r=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,i=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!r||l[1]>r[0]&&l[1]<r[3])){o.label=l[1];break}if(6===l[0]&&o.label<r[1]){o.label=r[1],r=l;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(l);break}r[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],i=0}finally{t=r=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var hn=(0,Z.observer)((function(n){var e,l,c,s,u=n.type,d=(0,G.B)().t,f=(0,$.P)(),p=R().styles,h=(0,r.useContext)(q.DesignContext),m=h.waitingToFurnishRemaining,x=(h.setWaitingToFurnishRemaining,W.A,un((0,r.useState)(!1),2)),g=(x[0],x[1]),v=un((0,r.useState)(!1),2),b=v[0],y=v[1],w=un((0,r.useState)(0),2),j=(w[0],w[1]),S=un((0,r.useState)(!1),2),_=(S[0],S[1]),k=(0,r.useRef)(null),C=((0,r.useRef)(null),(0,r.useRef)(null),(0,r.useRef)(null),un((0,r.useState)([]),2)),I=C[0],A=C[1],N=un((0,r.useState)([]),2),z=(N[0],N[1]),E=un((0,r.useState)([]),2),O=(E[0],E[1]),M=un((0,r.useState)(["全案风格"]),2),D=M[0],P=M[1],F=un((0,r.useState)((0,K.fZ)()?260:360),2),T=(F[0],F[1],un((0,r.useState)((null===(e=f.userStore.userInfo)||void 0===e?void 0:e.isFactory)?"2":"1"),2)),L=(T[0],T[1],un((0,r.useState)(1),2)),B=(L[0],L[1],un((0,r.useState)({}),2)),U=(B[0],B[1]),Z=un((0,r.useState)({}),2),rn=Z[0],on=Z[1],fn=un((0,r.useState)(m),2),hn=(fn[0],fn[1]),mn=un((0,r.useState)(!1),2),xn=mn[0],gn=mn[1],vn=un((0,r.useState)(null),2),bn=vn[0],yn=vn[1],wn=un((0,r.useState)([]),2),jn=wn[0],Sn=wn[1],_n=un((0,r.useState)([]),2),kn=_n[0],Cn=_n[1],In=un((0,r.useState)([]),2),An=In[0],Nn=In[1],zn=un((0,r.useState)([]),2),En=(zn[0],zn[1]),On=un((0,r.useState)("1"),2),Mn=On[0],Dn=On[1],Pn={"成品":["1","2","3","4","15","37","38"],"定制":["10","11","12","13","17","19","20","21","22","23","24","28"],"贴图":["8","14","26","18","25","36","41"]},Fn=un((0,r.useState)({orderBy:"sort asc",ruleType:(null===(l=f.userStore.userInfo)||void 0===l?void 0:l.isFactory)?2:1,pageSize:100,pageIndex:1,schemeKeyWord:"",ruleKeyWord:"",spaceName:null,schemeStyleId:"",ruleStyleId:"",queryType:2}),2),Tn=Fn[0],Ln=Fn[1];(0,r.useEffect)((function(){an((function(){var n,e,t,i;return pn(this,(function(r){switch(r.label){case 0:return y(!0),_(!0),n=Tn,[4,(0,H.Ic)(n)];case 1:return e=r.sent(),(t=null==e?void 0:e.result)&&t.forEach((function(n){var e;n.roomList=null==n||null===(e=n.ruleImageList)||void 0===e?void 0:e.map((function(n){return{imgPath:n}}))})),_(!1),y(!1),t?(i=(null==I?void 0:I.length)>0&&Tn.pageIndex>1?dn(I).concat(dn(t)):t,A(i)):A([]),g(!1),j(null==e?void 0:e.recordCount),[2]}}))}))()}),[Tn]),(0,r.useEffect)((function(){var n=function(n){n.ctrlKey&&"q"===n.key.toLowerCase()&&P("全案风格"===D[0]?["风格套系","样板间"]:["全案风格"])};return window.addEventListener("keydown",n),function(){window.removeEventListener("keydown",n)}}),[D]),(0,r.useEffect)((function(){hn(m)}),[m]);var Bn=function(n,e,t,i){f.schemeStatusStore.layoutSchemeSaved=!1,f.schemeStatusStore.pendingOpenSchemeIn3D=!1,Y.nb.DispatchEvent(Y.n0.SeriesSampleSelected,{series:n,scope:{soft:e,hard:t,cabinet:i,remaining:!1}}),f.homeStore.selectData&&f.homeStore.selectData.rooms&&Un(f.homeStore.selectData.rooms)},Un=function(n){var e=rn;e={};var t=!0,i=!1,r=void 0;try{for(var o,a=n[Symbol.iterator]();!(t=(o=a.next()).done);t=!0){var l=o.value;if(l._scope_series_map)for(var c in l._scope_series_map){var s=l._scope_series_map[c];s&&s.ruleId&&(e[s.ruleId]||(e[s.ruleId]={}),e[s.ruleId][c]=!0)}}}catch(n){i=!0,r=n}finally{try{t||null==a.return||a.return()}finally{if(i)throw r}}on(e)};(0,r.useEffect)((function(){g(!0),an((function(){var n,e,t;return pn(this,(function(i){switch(i.label){case 0:return[4,(0,V.kV)()];case 1:return(e=i.sent())?(t=null===(n=Object)||void 0===n?void 0:n.keys(e).map((function(n,e){return{id:e+1,screenName:n}})),O(t),[2]):[2]}}))}))(),an((function(){var n,e;return pn(this,(function(t){switch(t.label){case 0:return[4,(0,H.$f)()];case 1:return(n=t.sent())?(e=null==n?void 0:n.map((function(n){return{value:n.key,screenName:n.label}})),z(e),[2]):[2]}}))}))()}),[]);var Rn="SeriesCandidateList";(0,r.useEffect)((function(){return Y.nb.on_M(X.U.SelectingRoom,Rn,(function(n){Un(n.current_rooms||[]),setTimeout((function(){f.homeStore.setSelectData({rooms:null==n?void 0:n.current_rooms,clickOnRoom:!0})}),20)})),function(){Y.nb.off_M(X.U.SelectingRoom,Rn)}}),[]);var Wn=function(n){var e=n.list;return(0,i.jsx)("div",{className:p.materialList,children:e.map((function(n,e){return(0,i.jsx)("img",{width:80,height:80,src:"".concat(n.imagePath,"?x-oss-process=image/resize,m_fixed,h_80,w_80"),alt:""},e)}))})},Hn=function(){Sn([]),Cn([]),Nn([]),En([])},Vn=[{key:"1",label:d("定制模型"),children:(0,i.jsx)(Wn,{list:jn})},{key:"2",label:d("软装模型"),children:(0,i.jsx)(Wn,{list:kn})},{key:"3",label:d("硬装模型"),children:(0,i.jsx)(Wn,{list:An})}];return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:p.bottomPanel,children:[(0,i.jsxs)("div",{className:p.topSelect,children:[(0,i.jsx)("div",{className:"".concat(1==Tn.ruleType?"checked":""),style:{color:"".concat(1==Tn.ruleType?"#282828":"#959598")},onClick:function(){Ln((function(n){return sn(cn({},n),{ruleType:1,pageIndex:1})}))},children:d("平台套系")}),(0,i.jsx)("div",{className:"".concat(2==Tn.ruleType?"checked":""),style:{color:"".concat(2==Tn.ruleType?"#282828":"#959598")},onClick:function(){Ln((function(n){return sn(cn({},n),{ruleType:2,pageIndex:1})}))},children:d("企业套系")})]}),(0,i.jsx)("div",{className:p.roomListBar,children:(0,i.jsx)(J.A,{})}),(0,i.jsx)("div",{className:"".concat(p.container_listInfo," ").concat(b?p.noScroll:""," ").concat(u?p.type:""),ref:k,children:I&&I.length>0?(0,i.jsx)(i.Fragment,{children:null==I||null===(c=I.map)||void 0===c?void 0:c.call(I,(function(n,e){return(0,i.jsx)("div",{id:"series_box"+e,className:p.container_box,children:(0,i.jsxs)("div",{className:p.container_data,onMouseEnter:function(){return U((function(n){return sn(cn({},n),ln({},e,!0))}))},onMouseLeave:function(){return U((function(n){return sn(cn({},n),ln({},e,!1))}))},children:[(0,i.jsxs)("div",{className:"".concat(p.Popover_hoverInfo," ").concat(rn[n.ruleId]?p.Popover_hoverInfo_type:""),children:[(0,i.jsx)("img",{onClick:function(){!function(n,e){"软装"==n?Bn(e,!0,!1,!1):"硬装"==n?Bn(e,!1,!0,!1):"定制"==n&&Bn(e,!1,!1,!0)}(u,n),Bn(n,!0,!0,!0)},src:"".concat(n.thumbnail,"?x-oss-process=image/resize,m_fixed,h_218,w_318"),alt:""}),(0,i.jsx)(en.If,{condition:rn[n.ruleId],children:(0,i.jsx)("div",{className:p.tag_label,children:d("使用全部")})})]}),(0,i.jsxs)("div",{className:p.textInfo,children:[(0,i.jsx)("div",{className:p.container_title,title:n.seedSchemeName||n.ruleName,children:n.seedSchemeName||n.ruleName}),(0,i.jsx)("div",{className:p.container_desc,onClick:function(){return function(n){yn(n),tn.log("item",n),nn.VF.getSeriesAllMaterial(n.ruleId,(function(n){Sn(n.filter((function(n){return Pn["定制"].includes(String(n.modelFlag))}))),Cn(n.filter((function(n){return Pn["成品"].includes(String(n.modelFlag))}))),Nn(n.filter((function(n){return Pn["贴图"].includes(String(n.modelFlag))})))}),(function(){tn.log("err")})),gn(!0),Dn("1")}(n)},children:d("详情")})]})]},e)},"record_"+e)}))}):(0,i.jsx)("div",{className:p.emptyInfo,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("img",{src:t(78793),alt:""}),(0,i.jsx)("div",{className:"desc",children:d("暂无数据")})]})})})]}),(0,i.jsx)("div",{className:p.panelContainer,children:xn&&(0,i.jsx)(Q._w,{center:!0,className:p.panel,draggable:!0,title:(null==bn?void 0:bn.seedSchemeName)||(null==bn?void 0:bn.ruleName),onClose:function(){gn(!1),Hn()},mask:!0,children:(0,i.jsxs)("div",{className:p.panelContent,children:[(0,i.jsx)(o.A,{effect:"fade",autoplay:!0,children:null==bn||null===(s=bn.roomList)||void 0===s?void 0:s.map((function(n,e){return(0,i.jsx)("div",{children:(0,i.jsx)("img",{className:p.roomImg,src:"".concat(n.imgPath,"?x-oss-process=image/resize,m_fixed,h_290,w_520"),alt:""})},e)}))}),(0,i.jsxs)("div",{children:[(0,i.jsx)(a.A,{defaultActiveKey:"1",items:Vn,onChange:function(n){En("1"==n?jn:"2"==n?kn:An),Dn(n)}}),(0,i.jsxs)("div",{className:p.applyBtnInfo,children:[(0,i.jsxs)("button",{className:"leftBtn",onClick:function(){"1"==Mn?Bn(bn,!1,!1,!0):"2"==Mn?Bn(bn,!0,!1,!1):"3"==Mn&&Bn(bn,!1,!0,!1),gn(!1),Hn()},children:["应用",d("1"==Mn?"定制":"2"==Mn?"软装":"硬装")]}),(0,i.jsx)("button",{className:"rightBtn",onClick:function(){Bn(bn,!0,!0,!0),gn(!1),Hn()},children:d("应用全部")})]})]})]})})})]})}))},70060:function(n,e,t){t.d(e,{A:function(){return X}});var i=t(13274),r=t(2021),o=t(88934),a=t(48402),l=t(27347),c=t(8636),s=t(70524),u=t(5711),d=t(50060),f=t(52898),p=t(15696),h=t(41594),m=t.n(h),x=t(61643),g=t(5450),v=t(62625),b=t(81639);function y(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function w(){var n=y(["\n      display: grid;\n      max-height: 224px;\n      /* padding-top: 16px; */\n      /* transition: all .3s; */\n      overflow-y: auto;\n      overflow-x: hidden;\n      padding: 0 16px;\n      margin-bottom: 16px;\n      grid-template-columns: repeat(6, 1fr);\n      grid-template-rows: repeat(2, 1fr);\n      grid-auto-rows: 1fr;\n      gap: calc((10vw - 16px) / 6);\n      &::-webkit-scrollbar {\n        width: 0;\n        height: 0;\n      }\n      @media screen and (max-width: 450px) { // 手机宽度\n        grid-template-columns: repeat(4, 1fr);\n      }\n      @media screen and (orientation: landscape) {\n        grid-template-columns: repeat(2, 1fr);\n        max-height: calc(var(--vh, 1vh) * 100 - 280px);\n      }\n      .fold\n      {\n        width: 100%;\n        border-radius: 4px;\n        background: #F4F5F5;\n        height: 24px;\n        line-height: 24px;\n        font-weight: 600;\n        padding: 0 5px;\n        margin: 8px 0 4px 0;\n      }\n      // .content {\n      //   display: flex;\n      //   flex-wrap: nowrap;\n      //   transition: max-height 0.3s ease-in-out; /* 这将添加过渡动画 */\n      // }\n\n      // .collapsed {\n      //   max-height: 0;\n      // }\n      .item {\n        /* max-width: 30%; */\n        margin: 1vw 0;\n        width: 100%;\n        height: 16vw;\n        cursor: pointer;\n        transition: box-shadow 0.3s ease;\n        user-select:none;\n        @media screen and (max-width: 450px) { // 手机宽度\n          height: 28vw;\n        }\n        @media screen and (orientation: landscape) {\n          height: 9vw;\n        }\n        :nth-child(6n) {\n          margin-right: 0;\n        }\n        .image {\n          height: calc(15vw * 0.9);\n          width: 90%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-radius: 6px;\n          overflow:hidden;\n          /* transition: all .3s; */\n          background-color: #f5f5f5;\n          @media screen and (max-width: 450px) { // 手机宽度\n            height: calc(25vw * 0.9);\n          }\n          @media screen and (orientation: landscape) {\n            height: 100%;\n            width: 100%;\n          }\n          .ant-image-img {\n            vertical-align: middle;\n            user-select:none;\n            pointer-events: none; \n          }\n          .structure-image.ant-image-img {\n            vertical-align: middle;\n            user-select:none;\n            pointer-events: none; \n            height: 60px;\n            width: 60px;\n          }\n          .group_image.ant-image-img {\n            vertical-align: middle;\n            user-select:none;\n            pointer-events: none; \n          }\n\n        }\n\n        .title {\n          color: #000000;\n          font-size: 12px;\n          padding: 5px 0;\n          height: 40px;\n          line-height: 20px;\n          text-align: center;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n      }\n      .item:hover {\n          /* box-shadow: 3px 3px 8px 0px rgba(0, 0, 0, 0.12); */\n        }\n    "]);return w=function(){return n},n}function j(){var n=y(["\n      /* height: calc(100vh - 370px) !important; */\n    "]);return j=function(){return n},n}var S=(0,b.rU)((function(n){var e=n.css;return{figure:e(w()),mobile:e(j())}})),_=t(97082);function k(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function C(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return k(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return k(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var I=(0,p.observer)((function(n){var e=n.data,t=n.filterName,r=(0,x.B)().t,o=S().styles,a=((0,s.P)(),C((0,h.useState)(""),2)),c=a[0],d=a[1],f=m().useRef(null),p=C((0,h.useState)(),2),b=(p[0],p[1]);(0,h.useRef)([]);(0,h.useEffect)((function(){var n=new _.Jc({isShowThumbnail:!0,container:document.getElementById("side_pannel"),log:!1});return n.bindDrag(),function(){n.unbindDrag()}}),[]),(0,h.useEffect)((function(){b(e.map((function(){return!1})))}),[e]),(0,h.useEffect)((function(){var n=f.current,e=function(){c&&(l.nb.RunCommand(l._I.LeaveSubHandler),d(""))},t=function(n){var e=f.current.getBoundingClientRect();n.touches[0].clientY<e.bottom-e.height&&f.current&&(f.current.style.overflow="hidden")},i=function(n){f.current&&(f.current.style.overflow="scroll")};return n.addEventListener("mouseup",e),n.addEventListener("touchmove",t),n.addEventListener("touchend",i),function(){n.removeEventListener("mouseup",e),n.removeEventListener("touchmove",t),n.removeEventListener("touchend",i)}}),[c]);var y=m().memo(g.A);return(0,i.jsx)("div",{className:"".concat(o.figure),ref:f,id:"scrollContainerRef",children:e.map((function(n,e){return(0,i.jsx)(m().Fragment,{children:n.figureList.map((function(n,e){return(0,i.jsxs)("div",{className:"item",children:[(0,i.jsx)("div",{className:"image",onPointerDown:function(e){var t=n.title;n.group_code&&(t="GroupTemplate:"+n.group_code),n.title.includes("单开门")||n.title.includes("推拉门")||n.title.includes("一字窗")||n.title.includes("飘窗"),l.nb.DispatchEvent(l.n0.SelectedFurniture,t)},onPointerUp:function(e){var t=n.title;if(n.group_code)return t="GroupTemplate:"+n.group_code,void l.nb.DispatchEvent(l.n0.mobileAddFurniture,t);l.nb.DispatchEvent(l.n0.mobileAddFurniture,t)},children:(0,i.jsx)(y,{src:(0,u.$Y)()+"static/figures_imgs/".concat(n.png),preview:!1,alt:n.title,className:"结构件"===t?"structure-image":""})}),(0,i.jsx)(v.A,{placement:"rightBottom",title:r(n.title),children:(0,i.jsx)("div",{className:"title",children:r(n.title)})})]},e)}))},"figure_menu_key"+e)}))})}));function A(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function N(){var n=A(["\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100);\n        width: 224px;\n      }\n    "]);return N=function(){return n},n}function z(){var n=A(["\n\n    "]);return z=function(){return n},n}function E(){var n=A(["\n      display: flex;\n      flex-wrap: nowrap;\n      margin-top: 10px;\n      overflow-x: scroll;\n      padding: 0 16px;\n      &::-webkit-scrollbar {\n        display: none;\n      }\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n      @media screen and (orientation: landscape) {\n        flex-wrap: wrap;\n        padding: 0 12px;\n      }\n      .item\n      {\n        width: 66px;\n        height: 28px;\n        padding: 2px 10px;\n        font-weight: 600;\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        cursor: pointer;\n        margin: 2px 5px 2px 0;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n        @media screen and (orientation: landscape) {\n          margin: 2px 4px 2px 0px;\n          width: 61px;\n        }\n      }\n      img{\n        width: 16px;\n        height: 16px;\n      }\n      .active\n      {\n        border-radius: 4px;\n        background: #EAEAEB;\n      }\n    "]);return E=function(){return n},n}function O(){var n=A(["\n      width: 100%;\n      height: 1px;\n      background: #EAEAEB;\n      margin: 10px 0;\n    "]);return O=function(){return n},n}function M(){var n=A(["\n      display: flex;\n      margin-bottom: 4px;\n      overflow-x: scroll;\n      width: auto;\n      margin-left: 20px;\n      .item\n      {\n        color: #959598;\n        font-family: PingFang SC;\n        font-weight: regular;\n        font-size: 12px;\n        line-height: 1.67;\n        letter-spacing: 0px;\n        text-align: left;\n        margin-right: 16px;\n        white-space: nowrap;\n        cursor: pointer;\n      }\n      .active\n      {\n        color: #147FFA;\n      }\n    "]);return M=function(){return n},n}function D(){var n=A(["\n      box-sizing: border-box;\n      padding: 12px 12px 0 0;\n      transition: all .3s;\n      min-width: 156px;\n      ul {\n        padding: 0;\n      }\n      li {\n        padding: 0;\n        margin: 0;\n        list-style: none;\n      }\n      .menu {\n        > li {\n          margin-bottom: 16px;\n          transition: all .3s;\n        }\n        li:hover{\n          color: #5B5E60;\n        }\n        &_columns {\n          display: flex;\n        }\n\n        &_item {\n          /* background: #f2f2f2; */\n          padding: 8px 0;\n\n          :first-child {\n            margin-right: 12px;\n          }\n          \n          :last-child li:first-child {\n            width: 72px;\n          }\n          li {\n            // padding: 0 16px 0 22px;\n            margin: 8px 0;\n            color: #25282D;\n            font-family: PingFang SC;\n            font-weight: regular;\n            font-size: 14px;\n            line-height: 20px;\n            height: 20px;\n            width: 60px;\n            letter-spacing: 0px;\n            text-align: left;\n            cursor: pointer;\n            overflow: hidden;\n            white-space: nowrap;\n            text-overflow: ellipsis;\n            user-select:none;\n          }\n        }\n      }\n      .icon {\n        color: red;\n      }\n      .label {\n        color: #25282D;\n        font-weight: 600;\n        font-size: 16px;\n        line-height: 1.5;\n        letter-spacing: 0px;\n        text-align: left;\n        height: 24px;\n        display: flex;\n        align-items: center;\n        cursor: pointer;\n        user-select:none;\n\n        &_name {\n          // margin-left: 5px;\n          height: 100%;\n        }\n\n        &_name::after {\n          content: '';\n          height: 8px;\n          display: block;\n          position: relative;\n          top: -3px;\n          opacity: 0.5;\n          background: linear-gradient(90deg, #66B8FF 0%, #147FFA00 100%);\n        }\n\n        &.active {\n          color: rgba(20, 127, 250, 1);\n        }\n      }\n    "]);return D=function(){return n},n}function P(){var n=A(["\n      height: 100%;\n      /* min-width: 256px; */\n      .layout_btns {\n        display: flex;\n        align-items: center;\n        margin-top:10px;\n        padding-left:16px;\n\n        .btn {\n          border-radius: 2px;\n          background: rgba(20, 127, 250, 0.1);\n          color: rgba(20,127,250,1);\n          font-weight: bold;\n          font-size: 14px;\n          margin-bottom:8px;\n          width: 100px;\n          text-align: center;\n          &:first-child{\n            margin-right: 8px;\n          }\n          &:hover{\n            background: rgba(20, 127, 250, 0.25);\n            color: rgba(20,127,250,1);\n          }\n        }\n      }\n    "]);return P=function(){return n},n}function F(){var n=A(["\n      border-radius: 4px;\n      background: #F2F3F5;\n      color: #000;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 32px;\n      letter-spacing: 0px;\n      text-align: left;\n      // min-width: 326px;\n      width: 100%;\n      height: 32px;\n      border: none;\n      padding-left: 36px;\n      :focus {\n        border-color: none; /* 取消聚焦边框 */\n        box-shadow: none; /* 取消聚焦阴影效果 */\n        outline: none; /* 取消聚焦时的外边框效果 */\n      }\n    "]);return F=function(){return n},n}function T(){var n=A(["\n      position: absolute;\n      top: 50%;\n      translate: 16px -50%;\n    "]);return T=function(){return n},n}function L(){var n=A(["\n      position: absolute;\n      top: 8px;\n      right: 10px;\n      cursor: pointer;\n    "]);return L=function(){return n},n}function B(){var n=A(["\n      display: flex;\n    "]);return B=function(){return n},n}function U(){var n=A(["\n      display: flex;\n      align-items: center;\n    "]);return U=function(){return n},n}function R(){var n=A(["\n      display: flex;\n      flex-grow: 1;\n      position: relative;\n    "]);return R=function(){return n},n}function W(){var n=A(["\n      color: #147FFA !important;\n    "]);return W=function(){return n},n}function H(){var n=A(["\n      position: absolute;\n      top: 32px;\n      left: 0;\n      right: 0;\n      background: #fff;\n      border-radius: 4px;\n      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);\n      z-index: 100;\n      padding: 8px 0;\n      max-height: 300px;\n      overflow: auto;\n    "]);return H=function(){return n},n}var V=(0,b.rU)((function(n){var e=n.css;return{root:e(N()),menu_container:e(z()),tab_box:e(E()),line:e(O()),filterlist:e(M()),menu_box:e(D()),figure_box:e(P()),container_input:e(F()),Icon:e(T()),deleteIcon:e(L()),searchInfo:e(B()),closeInfo:e(U()),inputInfo:e(R()),select:e(W()),searchList:e(H())}}));function $(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function Y(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return $(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return $(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var X=(0,p.observer)((function(){var n=(0,s.P)(),e=(0,x.B)().t,t=V().styles,p=Y((0,h.useState)([]),2),m=p[0],g=p[1],v=Y((0,h.useState)(!1),2),b=v[0],y=v[1],w=Y((0,h.useState)(""),2),j=w[0],S=w[1],_=Y((0,h.useState)(""),2),k=_[0],C=_[1],A=Y((0,h.useState)([]),2),N=(A[0],A[1]),z=Y((0,h.useState)(!1),2),E=z[0],O=(z[1],Y((0,h.useState)([]),2)),M=O[0],D=(O[1],l.nb.IsDebug||n.userStore.beta?a.V:a.V.filter((function(n){return"视角"!==n.label&&"户型"!==n.label&&"定制"!==n.label}))),P=(0,i.jsx)(d.A,{style:{fontSize:24},spin:!0}),F=[],T=[];D.map((function(n){n.child.map((function(n){F=F.concat(n.figureList)}))})),D.filter((function(n){return"户型"!==n.label})).map((function(n,e){n.child.map((function(n){T=T.concat(n.figureList)}))}));var L=function(n){g(n.child)},B=function(){if(l.nb.instance._current_handler_mode===r.f.HouseDesignMode){var n,e,t=D.filter((function(n){return n.label.includes("户型")}));N(t),g(null===(e=t[0])||void 0===e||null===(n=e.child[0])||void 0===n?void 0:n.figureList)}else{var i=D.filter((function(n){return!0}));N(i)}};return(0,h.useEffect)((function(){L(D[0]),S(D[0].label),C(D[0].child[0].label),l.nb.on_M(o.U.AIDesignModeChanged,"FiguresMenu",(function(){B()})),B()}),[]),(0,h.useEffect)((function(){y(!0),setTimeout((function(){y(!1)}),200)}),[m]),(0,h.useEffect)((function(){l.nb.instance._current_handler_mode,r.f.HouseDesignMode}),[n.homeStore.selectedRoom]),(0,i.jsx)("div",{className:t.root,children:E?(0,i.jsx)(c.xS,{data:M}):(0,i.jsxs)("div",{className:t.menu_container,style:{opacity:E?0:1},children:[(0,i.jsx)("div",{className:t.tab_box,children:D.map((function(n){return(0,i.jsxs)("div",{className:"item ".concat((t=n.label,j===t?"active":"")),onClick:function(){L(n),S(n.label),C(n.child[0].label)},children:[(0,i.jsx)("img",{src:(0,u.$Y)()+"static/figures_imgs/".concat(n.png),alt:n.title}),(0,i.jsx)("span",{className:"label_name",children:e(n.label)})]},n.label);var t}))}),(0,i.jsx)("div",{className:t.figure_box,children:(0,i.jsx)(f.A,{indicator:P,spinning:b,children:(0,i.jsx)(I,{data:m,filterName:k})})})]})})}))},76135:function(n,e,t){t.d(e,{A:function(){return S}});var i=t(13274),r=t(41594);function o(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function a(){var n=o(["\n      height: 300px;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 85);\n        width: 224px;\n        margin-top: -40px;\n        background: #fff;\n        position: absolute;\n      }\n    "]);return a=function(){return n},n}function l(){var n=o(["\n      height: 40px;\n    "]);return l=function(){return n},n}function c(){var n=o(["\n      position: fixed;\n      top: 50px;\n      right: 20px;\n      z-index: 1200\n    "]);return c=function(){return n},n}function s(){var n=o(["\n      padding: 0 20px;\n      display: flex;\n      justify-content: space-between;\n      @media screen and (orientation: landscape) {\n        flex-direction: column;\n      }\n      .svg-input-number\n      {\n        padding-right: 4px;\n        @media screen and (max-width: 450px) { // 手机宽度\n          width: 65px;\n          font-size: 12px;\n        }\n      }\n      .left\n      {\n        width: 46%;\n        .title{\n          margin: 12px 0 6px 0px;\n        }\n        .houseInfo\n        {\n          .input\n          {\n            width: 50px;\n            margin-right: 4px;\n            @media screen and (max-width: 450px) { // 手机宽度\n              width: 45px;\n              margin-top: 10px;\n            }\n          }\n\n          span{\n            margin-right: 13px;\n            font-size: 14px;\n          }\n        }\n        .leftInfo\n        {\n          display: flex;\n        }\n\n      }\n      .right{\n        width: 46%;\n        .title{\n          margin: 12px 0 6px 0px;\n        }\n        .rightInfo\n        {\n          display: flex;\n          \n        }\n      }\n      .title{\n        @media screen and (orientation: landscape) {\n          font-size: 14px;\n          margin: 16px 0 16px 0px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n      }\n      .houseInfo{\n        @media screen and (orientation: landscape) {\n          display: flex;\n          flex: 0 0 50%;\n          font-size: 14px;\n          flex-wrap: wrap;\n          align-items: center;\n          div{\n            display: flex;\n            align-items: center;\n            margin-right: 8px;\n            margin-bottom: 12px;\n            align-items: center;\n            .input{\n              width: 64px;\n            }\n            span{\n              margin-left: 4px;\n            }\n          }\n        }\n      }\n    "]);return s=function(){return n},n}var u=(0,t(81639).rU)((function(n){var e=n.css;return{root:e(a()),roomListBar:e(l()),name:e(c()),attributeInfo:e(s())}})),d=t(27347),f=t(15696),p=t(61643),h=t(70524),m=t(49944),x=t(40561),g=t(38775),v=t(14368),b=t(63286),y=t(17365);function w(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function j(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return w(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return w(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var S=(0,f.observer)((function(){var n=(0,h.P)(),e=(0,p.B)().t,t=u().styles,o=j((0,r.useState)(0),2),a=(o[0],o[1],j((0,r.useState)(-1),2)),l=(a[0],a[1],j((0,r.useState)(""),2)),c=(l[0],l[1],j((0,r.useState)({}),2)),s=(c[0],c[1],j((0,r.useState)(0),2)),f=(s[0],s[1],j((0,r.useState)(!1),2)),w=(f[0],f[1],j((0,r.useState)({}),2)),S=w[0],_=w[1],k=j((0,r.useState)(),2),C=k[0],I=k[1],A=j((0,r.useState)(),2),N=A[0],z=A[1],E=j((0,r.useState)(),2),O=E[0],M=E[1],D=j((0,r.useState)(),2),P=D[0],F=D[1],T=j((0,r.useState)(),2),L=T[0],B=T[1],U=j((0,r.useState)(0),2),R=U[0],W=U[1],H=j((0,r.useState)(0),2),V=H[0],$=H[1],Y=j((0,r.useState)(0),2),X=Y[0],q=Y[1],G=j((0,r.useState)(0),2),K=G[0],Z=G[1],J=j((0,r.useState)(0),2),Q=J[0],nn=J[1],en=j((0,r.useState)(1),2);en[0],en[1];(0,r.useEffect)((function(){var t,i,r,o,a,l,c,s,u,d,f,p,h,m,x,g,v;"空间信息"!=(null===(t=n.homeStore.attribute)||void 0===t?void 0:t.title)&&(null===(i=n.homeStore.attribute)||void 0===i?void 0:i.tile)!=e("空间信息")||(_(n.homeStore.attribute.properties),I(null===(a=n.homeStore.attribute)||void 0===a||null===(o=a.properties)||void 0===o||null===(r=o.name)||void 0===r?void 0:r.defaultValue),M(null===(s=n.homeStore.attribute)||void 0===s||null===(c=s.properties)||void 0===c||null===(l=c.storey_height)||void 0===l?void 0:l.defaultValue),z(null===(f=n.homeStore.attribute)||void 0===f||null===(d=f.properties)||void 0===d||null===(u=d.ceiling_height)||void 0===u?void 0:u.defaultValue),F(null===(m=n.homeStore.attribute)||void 0===m||null===(h=m.properties)||void 0===h||null===(p=h.floor_thickness)||void 0===p?void 0:p.defaultValue),B(null===(v=n.homeStore.attribute)||void 0===v||null===(g=v.properties)||void 0===g||null===(x=g.max_cabinet_height)||void 0===x?void 0:x.defaultValue))}),[n.homeStore.attribute]),(0,r.useEffect)((function(){if(n.homeStore.roomInfos){var e=0,t=0,i=0,r=0,o=0;n.homeStore.roomInfos.forEach((function(n){e+=n.area,(n.name.includes("室")||n.name.includes("卧"))&&t++,n.name.includes("厅")&&i++,(n.name.includes("厕所")||n.name.includes("卫生间"))&&r++,n.name.includes("厨房")&&o++})),Z(parseFloat(e.toFixed(2))),W(t),$(i),q(r),nn(o),M(d.nb.instance.layout_container._storey_height)}}),[n.homeStore.roomInfos]);var tn=(0,r.useCallback)((function(e,t){return function(i){var r;e(i),null==S||null===(r=S[t])||void 0===r||r.onChange(i),"name"===t&&(y.f.updateAliasName(d.nb.instance.layout_container),n.homeStore.setRoomEntites(d.nb.instance.layout_container._room_entities))}}),[S]);return(0,i.jsx)("div",{className:t.root,children:(0,i.jsx)("div",{className:t.attributeInfo,children:n.homeStore.selectedRoom?(0,i.jsx)(i.Fragment,{children:n.homeStore.IsLandscape?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"title",children:e("空间类型")}),(0,i.jsx)("div",{children:(0,i.jsx)(v.A,{value:C,style:{width:"100%"},onChange:tn(I,"name"),options:b.H.getRoomNameOptions(),getPopupContainer:function(n){return n.parentNode}})}),(0,i.jsxs)("div",{className:"title",children:[e("当前层高"),(0,i.jsx)(g.A,{min:0,max:2800,disabled:!0,style:{width:"100px"},value:O,onChange:function(n){return tn(M,"storey_height")(Number(n))},suffix:"mm"})]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"left",children:[(0,i.jsx)("div",{className:"title",children:e("空间类型")}),(0,i.jsx)("div",{children:(0,i.jsx)(v.A,{value:C,style:{width:"100%"},onChange:tn(I,"name"),options:b.H.getRoomNameOptions()})}),(0,i.jsx)("div",{className:"title",children:e("当前层高")}),(0,i.jsxs)("div",{className:"leftInfo",children:[(0,i.jsx)(x.A,{min:0,max:2800,onChange:tn(M,"storey_height"),style:{width:"100%"},value:O,disabled:!0}),(0,i.jsx)(g.A,{min:0,max:2800,disabled:!0,style:{width:"100px"},value:O,onChange:function(n){return tn(M,"storey_height")(Number(n))},suffix:"mm"})]}),(0,i.jsx)("div",{className:"title",children:e("地铺厚度")}),(0,i.jsxs)("div",{className:"leftInfo",children:[(0,i.jsx)(x.A,{min:0,max:100,onChange:tn(F,"floor_thickness"),style:{width:"100%"},value:P}),(0,i.jsx)(g.A,{min:0,max:100,style:{width:"100px"},value:P,onChange:function(n){return tn(F,"floor_thickness")(Number(n))},suffix:"mm"})]})]}),(0,i.jsxs)("div",{className:"right",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"title",children:e("最高柜顶高")}),(0,i.jsx)("div",{children:(0,i.jsx)(m.A,{disabled:!0,style:{width:"100%"},value:L,suffix:"mm"})})]}),(0,i.jsx)("div",{className:"title",children:e("吊顶下吊")}),(0,i.jsxs)("div",{className:"rightInfo",children:[(0,i.jsx)(x.A,{min:200,max:400,onChange:tn(z,"ceiling_height"),style:{width:"100%"},value:N}),(0,i.jsx)(g.A,{min:200,max:400,style:{width:"100px"},value:N,onChange:function(n){return tn(z,"ceiling_height")(Number(n))},suffix:"mm"})]})]})]})}):(0,i.jsx)(i.Fragment,{children:n.homeStore.IsLandscape?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"title",children:e("房屋使用面积")}),(0,i.jsx)("div",{children:(0,i.jsx)(m.A,{disabled:!0,style:{width:"100%"},value:K,suffix:"m²"})})]}),(0,i.jsx)("div",{className:"title",children:e("户型")}),(0,i.jsxs)("div",{className:"houseInfo",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(m.A,{className:"input",disabled:!0,value:R}),(0,i.jsx)("span",{children:e("室")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(m.A,{className:"input",disabled:!0,value:V}),(0,i.jsx)("span",{children:e("厅")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(m.A,{className:"input",disabled:!0,value:X})," ",(0,i.jsx)("span",{children:e("卫")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(m.A,{className:"input",disabled:!0,value:Q})," ",(0,i.jsx)("span",{children:e("厨")})]})]}),(0,i.jsxs)("div",{className:"title",children:[e("当前层高"),(0,i.jsx)(g.A,{min:2e3,max:3500,style:{width:"100px"},value:O,onChange:function(n){var e=Number(n);e>=2200&&e<=6e3&&(d.nb.instance.layout_container._storey_height=e),M(n)},suffix:"mm"})]}),(0,i.jsx)("div",{className:"rightInfo",children:(0,i.jsx)(x.A,{min:2e3,max:3500,onChange:function(n){var e=Number(n);e>=2200&&e<=6e3&&(d.nb.instance.layout_container._storey_height=e),M(n)},style:{width:"100%"},value:O})})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"left",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"title",children:e("房屋使用面积")}),(0,i.jsx)("div",{children:(0,i.jsx)(m.A,{disabled:!0,style:{width:"100%"},value:K,suffix:"m²"})})]}),(0,i.jsx)("div",{className:"title",children:e("户型")}),(0,i.jsxs)("div",{className:"houseInfo",children:[(0,i.jsx)(m.A,{className:"input",disabled:!0,value:R}),(0,i.jsx)("span",{children:e("室")}),(0,i.jsx)(m.A,{className:"input",disabled:!0,value:V}),(0,i.jsx)("span",{children:e("厅")}),(0,i.jsx)(m.A,{className:"input",disabled:!0,value:X}),(0,i.jsx)("span",{children:e("卫")}),(0,i.jsx)(m.A,{className:"input",disabled:!0,value:Q}),(0,i.jsx)("span",{children:e("厨")})]})]}),(0,i.jsxs)("div",{className:"right",children:[(0,i.jsx)("div",{className:"title",children:e("当前层高")}),(0,i.jsxs)("div",{className:"rightInfo",children:[(0,i.jsx)(x.A,{min:2e3,max:3500,onChange:function(n){var e=Number(n);e>=2200&&e<=6e3&&(d.nb.instance.layout_container._storey_height=e),M(n)},style:{width:"50%"},value:O}),(0,i.jsx)(g.A,{min:2e3,max:3500,style:{width:"100px"},value:O,onChange:function(n){var e=Number(n);e>=2200&&e<=6e3&&(d.nb.instance.layout_container._storey_height=e),M(n)},suffix:"mm"})]})]})]})})})})}))},78154:function(n,e,t){t.d(e,{$:function(){return _},A:function(){return k}});var i=t(13274),r=t(61643);function o(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function a(){var n=o(["\n      position:fixed;\n      left:0;\n      bottom:0px;\n      width:100%;\n      overflow: hidden;\n      background-color: #fff;\n      border-radius: 16px 16px 0px 0px;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      z-index: 99;\n      @media screen and (orientation: landscape) {\n        position:fixed;\n        left: -1px !important;\n        top: 52px !important;\n        bottom: 0 !important;\n        right: auto !important;\n        max-height: calc(var(--vh, 1vh) * 100);\n        width: auto;\n        border-radius: 0px;\n        box-shadow: 0px 0px 16px 10px #0000000A;\n      }\n    "]);return a=function(){return n},n}function l(){var n=o(["\n      display: flex;\n      height: 40px;\n      padding: 0 24px;\n      align-items: center;\n      font-size: 20px;\n      color: #282828;\n      font-weight: 600;\n      margin-top: 16px;\n      justify-content: space-between;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 15px;\n        font-size: 16px;\n      }\n      @media screen and (orientation: landscape) {\n        height: 40px;\n        font-size: 14px;\n      }\n    "]);return l=function(){return n},n}function c(){var n=o(["\n      height:100%;\n      width:100%;\n    "]);return c=function(){return n},n}function s(){var n=o(["\n      position: absolute; \n      right: 10px;\n      top: 10px;\n      z-index: 9;\n    "]);return s=function(){return n},n}var u=(0,t(81639).rU)((function(n){var e=n.css;return{root:e(a()),topTitle:e(l()),listContainer:e(c()),open:e(s())}})),d=t(15696),f=t(41594),p=t(27347),h=t(45599),m=t(50617),x=t(70060),g=t(76135),v=t(76330),b=t(93491),y=t(70524),w=t(81074);function j(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function S(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return j(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return j(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var _=function(n){return n.setIsVisible="setIsVisible",n.showPopup="showPopup",n}({}),k=(0,d.observer)((function(){var n,e=(0,r.B)().t,t=u().styles,o=S((0,f.useState)(!0),2),a=(o[0],o[1],S((0,f.useState)(""),2)),l=a[0],c=a[1],s=S((0,f.useState)(!1),2),d=s[0],j=s[1],_=(0,y.P)();(0,f.useEffect)((function(){p.nb.on("showPopup",(function(n){n?(j(!0),c(n)):j(!1)}))}),[]);return(0,i.jsxs)("div",{className:t.root,style:{zIndex:10,maxHeight:_.homeStore.IsLandscape&&(d?"800px":"0px"),maxWidth:!_.homeStore.IsLandscape&&(d?"376px":"0px"),transition:"all 0.3s ease"},children:["view"!=l&&(0,i.jsxs)("div",{className:t.topTitle,children:[(0,i.jsx)("div",{children:function(n){switch(n){case"Layout":return e("布局");case"Matching":return e("");case"view":return e("视角");case"material":return e("素材");case"attribute":return e("属性");case"replace":return e("替换素材");case"searchMaterial":return e("空间素材");default:return""}}(l)}),(0,i.jsx)("div",{children:(0,i.jsx)(v.A,{style:{color:"#959598"},type:"icon-icon",onClick:function(){var n;j(!1),"3D_FirstPerson"===_.homeStore.viewMode&&"Furniture"===(null===(n=_.homeStore.selectEntity)||void 0===n?void 0:n.type)&&p.nb.DispatchEvent(p.n0.cleanSelect,null)}})})]}),(0,i.jsxs)("div",{className:t.listContainer,style:{display:"Layout"===l?"block":"none"},children:[" ",(0,i.jsx)(h.A,{width:400,showSchemeName:!1,isLightMobile:!0})," "]}),(0,i.jsxs)("div",{className:t.listContainer,style:{display:"Matching"===l?"block":"none"},children:[" ",(0,i.jsx)(m.A,{})," "]}),(0,i.jsxs)("div",{className:t.listContainer,style:{display:"material"===l?"block":"none"},children:[" ",(0,i.jsx)(x.A,{})," "]}),(0,i.jsxs)("div",{className:t.listContainer,style:{display:"attribute"===l?"block":"none"},children:[" ",(0,i.jsx)(g.A,{})," "]}),(0,i.jsxs)("div",{className:t.listContainer,style:{display:"replace"===l?"block":"none"},children:[" ",(0,i.jsx)(b.A,{selectedFigureElement:null===(n=_.homeStore.selectEntity)||void 0===n?void 0:n.figure_element})," "]}),(0,i.jsxs)("div",{className:t.listContainer,style:{display:"searchMaterial"===l?"block":"none"},children:[" ",(0,i.jsx)(w.A,{})," "]})]})}))},81074:function(n,e,t){t.d(e,{A:function(){return F}});var i=t(13274),r=t(61643),o=t(15696),a=t(41594);function l(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function c(){var n=l(["\n      height: 510px;\n      @media screen and (max-width: 450px) {\n        height: 380px;\n      }\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100);\n        width: 224px;\n      }\n    "]);return c=function(){return n},n}function s(){var n=l(["\n      display: flex;\n      padding: 0 24px;\n      margin-top: 20px;\n      justify-content: space-between;\n      @media screen and (orientation: landscape) {\n        display: block;\n      }\n    "]);return s=function(){return n},n}function u(){var n=l(["\n      width: 32%;\n      height: 80px;\n      border-radius: 8px;\n      background: #F4F5F5;\n      position: relative;\n      overflow: hidden;\n      border: 1px solid #0000000F;\n      @media screen and (orientation: landscape) {\n        width: 100%;\n        margin-bottom: 8px;\n      }\n      .add\n      {\n        left: 50%;\n        top: 50%;\n        position: absolute;\n        transform: translate(-50%, -50%);\n        color: #282828;\n        font-size: 12px;\n        width: 100%;\n        text-align: center;\n      }\n      img{\n        width: 50%;\n        height: 80px;\n        margin-right: 8px;\n        @media screen and (orientation: landscape) {\n          width: 30%;\n          height: 80px;\n        }\n      }\n      .item\n      {\n        display: flex;\n        justify-content: space-between;\n        position: relative;\n        @media screen and (orientation: landscape) {\n          justify-content: start;\n        }\n        .title\n        {\n          width: 50%;\n          position: absolute;\n          text-align: center;\n          bottom: 0px;\n          height: 20px;\n          color: #FFF;\n          border-radius: 0px 0px 4px 4px;\n          background: #00000066;\n          padding-top: 2px;\n          @media screen and (orientation: landscape) {\n            width: 30%;\n          }\n        }\n      }\n      .rightitem\n      {\n\n        width: 50%;\n        display: flex;\n        flex-direction: column;\n        padding: 8px 4px;\n        justify-content: space-between;\n        position: relative;\n        color: #282828;\n        .icon\n        {\n          position: absolute;\n          right: 10px;\n          bottom: 10px;\n          font-size: 18px;\n          color: #5B5E60;\n        }\n        .seriesStyle\n        {\n          border-radius: 4px;\n          background: #FFFFFF;\n          padding: 4px 8px;\n          font-size: 12px;\n          color: #5B5E60;\n          width: 40px;\n          text-align: center;\n          height: 24px;\n        }\n      }\n    "]);return u=function(){return n},n}function d(){var n=l(["\n     overflow-y: scroll;\n     height: calc(100% - 100px);\n     margin-top: 24px;\n     padding: 0 24px;\n     @media screen and (orientation: landscape) {\n      height: calc(var(--vh, 1vh) * 100 - 350px);\n     }\n    .itemInfo {\n        margin-bottom: 16px; /* 每个 item 之间的间距 */\n        overflow: hidden; /* 隐藏溢出内容 */\n\n        .itemList\n        {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 16px;\n          .item{\n            text-align: center;\n            overflow: hidden;\n            width: 104px;\n            position: relative;\n            .redIcon\n            {\n              position: absolute;\n              top: 5px;\n              left: 5px;\n              color: #FF4D4F;\n            }\n            @media screen and (max-width: 450px){\n              width: 82px;\n            }\n            @media screen and (max-width: 400px){\n              width: 69px;\n            }\n            @media screen and (orientation: landscape) {\n              width: 69px;\n            }\n            img{\n              aspect-ratio: 1 / 1;\n              margin-bottom: 4px;\n            }\n            div{\n              text-overflow: ellipsis;\n              white-space: nowrap;\n              overflow: hidden;\n            }\n          }\n          \n        }\n    }\n\n    .header {\n        display: flex;\n        justify-content: space-between; /* 使 label 和 icon 分开 */\n        padding: 12px 0px; /* 内边距 */\n        .title\n        {\n          font-weight: 600;\n          font-size: 14px;\n        }\n    }\n\n    .item {\n        \n    }\n\n    .item img {\n        width: 100%; /* 图片宽度占满 */\n        height: auto; /* 高度自适应 */\n        background-color: #F4F5F5;\n        border-radius: 4px;\n    }\n    &::-webkit-scrollbar {\n        display: none;\n    }\n  \n    scrollbar-width: none;\n    -ms-overflow-style: none;\n    "]);return d=function(){return n},n}function f(){var n=l(["\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: rgba(0, 0, 0, 0.5);\n      display: flex;\n      justify-content: center;\n      align-items: flex-end;\n      z-index: 10002;\n      @media screen and (orientation: landscape) {\n        justify-content: end;\n        position: fixed;\n\n      }\n    "]);return f=function(){return n},n}function p(){var n=l(["\n      background-color: white;\n      width: 100%;\n      padding: 20px;\n      height: 300px;\n      padding-top: 56px;\n      border-radius: 16px 16px 0px 0px;\n      background: #FFFFFF;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      position: relative;\n      @media screen and (orientation: landscape) {\n        position: fixed;\n        bottom: 12px;\n        right: 0;\n        top: 45px;\n        left: auto;\n        padding: 0;\n        width: 224px;\n        border-radius: 0px;\n        overflow: hidden;\n        height: auto;\n        border-radius: 6px;\n      }\n    "]);return p=function(){return n},n}function h(){var n=l(["\n      position: absolute;\n      bottom: 0;\n      right: 0;\n      width: 100%;\n      height: 586px;\n      background-color: white;\n      box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);\n      transition: transform 0.2s ease;\n      border-radius: 16px 16px 0px 0px;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      @media screen and (orientation: landscape) {\n        height: calc(100%);\n        border-radius: 0px;\n        width: 224px;\n        left : 0px;\n        right : auto;\n      }\n      .sideTopInfo\n      {\n        padding: 24px;\n        display: flex;\n        justify-content: space-between;\n        div{\n          font-size: 20px;\n          font-weight: 600;\n          color: #282828;\n          margin-left: 6px;\n        }\n      }\n    "]);return h=function(){return n},n}function m(){var n=l(["\n        display: flex;\n        justify-content: space-between;\n        margin-top: 20px;\n        padding: 0 24px;\n        .info\n        {\n          display: flex;\n          img{\n            width: 72px;\n            height: 72px;\n            border-radius: 4px;\n            margin-right: 16px;\n          }\n        }\n         .sizeInfo\n         {\n          padding: 8px 0px;\n            .size\n            {\n              color: #5B5E60;\n              margin-top: 4px;\n            }\n         } \n      "]);return m=function(){return n},n}function x(){var n=l(["\n      transform: translateX(0);\n      @media screen and (orientation: landscape) {\n        transform: translateX(0);        \n      }\n    "]);return x=function(){return n},n}function g(){var n=l(["\n      transform: translateX(100%);\n      @media screen and (orientation: landscape) {\n        transform: translateX(-100%);\n      }\n    "]);return g=function(){return n},n}function v(){var n=l(["\n      position: absolute;\n      right: 7px;\n      top: 57%;\n      font-size: 16px;\n      color: #5B5E60;\n    "]);return v=function(){return n},n}function b(){var n=l(["\n      position: absolute;\n      left: 5px;\n      top: 5px;\n      font-size: 16px;\n      color: #FFAA00;\n    "]);return b=function(){return n},n}var y=(0,t(81639).rU)((function(n){var e=n.css;return{root:e(c()),styleInfo:e(s()),styleItem:e(u()),materialInfo:e(d()),visible:e(f()),serialsInfo:e(p()),sideVisible:e(h()),topInfo:e(m()),slideIn:e(x()),slideOut:e(g()),lock_icon:e(v()),warn_icon:e(b())}})),w=t(70524),j=t(76330),S=t(27347),_=t(5711),k=t(48402),C=t(50617),I=t(87248),A=t(93491),N=t(88934);function z(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function E(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function O(n,e){return e=null!=e?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):function(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,i)}return t}(Object(e)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))})),n}function M(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||P(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(n){return function(n){if(Array.isArray(n))return z(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||P(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function P(n,e){if(n){if("string"==typeof n)return z(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?z(n,e):void 0}}var F=(0,o.observer)((function(){var n,e,t,o,l,c,s,u,d,f,p,h,m,x,g,v=(0,r.B)().t,b=y().styles,z=(0,w.P)(),P=M((0,a.useState)([]),2),F=(P[0],P[1]),T=M((0,a.useState)([]),2),L=T[0],B=T[1],U=M((0,a.useState)([]),2),R=U[0],W=U[1],H=M((0,a.useState)(""),2),V=H[0],$=H[1],Y=M((0,a.useState)(!1),2),X=Y[0],q=Y[1],G=M((0,a.useState)(0),2),K=G[0],Z=G[1],J=M((0,a.useState)(null),2),Q=J[0],nn=J[1],en=M((0,a.useState)({"定制素材":!0,"软装素材":!0,"硬装素材":!0}),2),tn=en[0],rn=en[1],on=M((0,a.useState)(!1),2),an=on[0],ln=on[1],cn=function(n){rn((function(e){return O(function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),i.forEach((function(e){E(n,e,t[e])}))}return n}({},e),E({},n,!e[n]))}))},sn=[],un=[],dn=[],fn=[];return S.nb.instance&&S.nb.on(N.U.RoomMaterialsUpdated,(function(){Z(K+1)})),(0,a.useEffect)((function(){!function(){var n,e,t,i,r,o,a,l,c,s,u,d,f,p,h,m,x,g,b,y,w,j,C,I=[];if(z.homeStore.selectEntity){var A=null===(t=z.homeStore.selectedRoom)||void 0===t?void 0:t._room;if(A){var N=D(A._furniture_list);A._furniture_list.forEach((function(e){return(n=N).push.apply(n,D(e.getAlternativeFigureElements()))})),N.sort((function(n,e){return e.default_drawing_order-n.default_drawing_order})),N.forEach((function(n){var e,t,i,r,o,a;if(S.nb.IsDebug||!n._is_decoration&&!n._is_sub_board)if(n.haveMatchedMaterial()||n.haveDeletedMaterial()||n.sub_category.includes("组合")){if("Electricity"!==n._decoration_type&&(n.haveMatchedMaterial()||n.haveDeletedMaterial()||!n.sub_category.includes("组合"))){var l=(null===(e=n._matched_material)||void 0===e?void 0:e.imageUrl)||(0,_.$Y)()+"static/figures_imgs/square_pillar.svg",c=(null===(t=n._matched_material)||void 0===t?void 0:t.modelId)||"无",s={image_path:l,title:v(n.sub_category)+" "+v("素材ID:")+c,centerTitle:n._matched_material.name,bottomTitle:"".concat(Math.round(null==n||null===(i=n._matched_material)||void 0===i?void 0:i.length),"*").concat(Math.round(null==n||null===(r=n._matched_material)||void 0===r?void 0:r.width),"*").concat(Math.round(null==n||null===(o=n._matched_material)||void 0===o?void 0:o.height)),figure_element:n,room:A};n.haveMatchedCustomCabinet()?fn.push(s):un.push(s)}}else sn.push({image_path:(null===(a=k.eW[n.sub_category])||void 0===a?void 0:a.png)||(0,_.$Y)()+"static/figures_imgs/square_pillar.svg",title:v(n.modelLoc)+" | "+v(n.sub_category),centerTitle:v(n.modelLoc),bottomTitle:"".concat(Math.round(n.length),"*").concat(Math.round(n.depth),"*").concat(Math.round(n.height)),figure_element:n,room:A})})),(e=un).unshift.apply(e,D(sn)),A.getHardDecorationList().forEach((function(n){var e,t,i,r,o;if(n.haveMatchedMaterial()){var a=(null===(e=n._matched_material)||void 0===e?void 0:e.imageUrl)||(0,_.$Y)()+"static/figures_imgs/square_pillar.svg",l=(null===(t=n._matched_material)||void 0===t?void 0:t.modelId)||"无";dn.push({image_path:a,title:v(n.sub_category)+" "+v("素材ID:")+l,centerTitle:n._matched_material.name,bottomTitle:"".concat(Math.round(null==n||null===(i=n._matched_material)||void 0===i?void 0:i.length),"*").concat(Math.round(null==n||null===(r=n._matched_material)||void 0===r?void 0:r.width),"*").concat(Math.round(null==n||null===(o=n._matched_material)||void 0===o?void 0:o.height)),figure_element:n})}})),I.push({title1:(null===(r=A._scope_series_map)||void 0===r||null===(i=r.soft)||void 0===i?void 0:i.ruleName)||null,title2:(null===(a=A._scope_series_map)||void 0===a||null===(o=a.cabinet)||void 0===o?void 0:o.ruleName)||null,title3:(null===(c=A._scope_series_map)||void 0===c||null===(l=c.hard)||void 0===l?void 0:l.ruleName)||null,img1:(null===(u=A._scope_series_map)||void 0===u||null===(s=u.soft)||void 0===s?void 0:s.thumbnail)||null,img2:(null===(f=A._scope_series_map)||void 0===f||null===(d=f.cabinet)||void 0===d?void 0:d.thumbnail)||null,img3:(null===(h=A._scope_series_map)||void 0===h||null===(p=h.hard)||void 0===p?void 0:p.thumbnail)||null,softseriesStyle:null===(x=A._scope_series_map)||void 0===x||null===(m=x.soft)||void 0===m?void 0:m.seriesName,cabinetseriesStyle:null===(b=A._scope_series_map)||void 0===b||null===(g=b.cabinet)||void 0===g?void 0:g.seriesName,hardseriesStyle:null===(w=A._scope_series_map)||void 0===w||null===(y=w.hard)||void 0===y?void 0:y.seriesName,bottomTitle:A.roomname,area:null===(j=A.area)||void 0===j?void 0:j.toFixed(2),room:A});var E=[{label:v("风格"),figureList:I},{label:v("定制素材"),figureList:fn},{label:v("软装素材"),figureList:un},{label:v("硬装素材"),figureList:dn}];F(E),B(null===(C=E[0])||void 0===C?void 0:C.figureList),W(E.filter((function(n,e){return 0!==e})))}}}()}),[z.homeStore.selectEntity,z.homeStore.room2SeriesSampleArray,K,X]),(0,i.jsxs)("div",{className:b.root,children:[(0,i.jsxs)("div",{className:b.styleInfo,children:[(0,i.jsx)("div",{className:b.styleItem,onClick:function(){ln(!0),$("软装")},children:(null===(n=L[0])||void 0===n?void 0:n.title1)&&(null===(e=L[0])||void 0===e?void 0:e.img1)?(0,i.jsxs)("div",{className:"item",children:[(0,i.jsx)("img",{src:null===(t=L[0])||void 0===t?void 0:t.img1,alt:""}),(0,i.jsx)("div",{className:"title",children:v("软装")}),(0,i.jsxs)("div",{className:"rightitem",children:[(0,i.jsx)("span",{children:null===(o=L[0])||void 0===o?void 0:o.title1}),(0,i.jsx)("span",{className:"seriesStyle",children:null===(l=L[0])||void 0===l?void 0:l.softseriesStyle})]})]}):(0,i.jsxs)("span",{className:"add",children:[(0,i.jsx)(j.A,{style:{color:"#5B5E60",fontSize:"14px",marginRight:"6px"},type:"icon-anzhuangInstall"}),v("添加软装风格")]})}),(0,i.jsx)("div",{className:b.styleItem,onClick:function(){ln(!0),$("定制")},children:(null===(c=L[0])||void 0===c?void 0:c.title2)&&(null===(s=L[0])||void 0===s?void 0:s.img2)?(0,i.jsxs)("div",{className:"item",children:[(0,i.jsx)("img",{src:null===(u=L[0])||void 0===u?void 0:u.img2,alt:""}),(0,i.jsx)("div",{className:"title",children:v("定制")}),(0,i.jsxs)("div",{className:"rightitem",children:[(0,i.jsx)("span",{children:null===(d=L[0])||void 0===d?void 0:d.title2}),(0,i.jsx)("span",{className:"seriesStyle",children:null===(f=L[0])||void 0===f?void 0:f.cabinetseriesStyle})]})]}):(0,i.jsxs)("span",{className:"add",children:[(0,i.jsx)(j.A,{style:{color:"#5B5E60",fontSize:"14px",marginRight:"6px"},type:"icon-anzhuangInstall"}),v("添加定制风格")]})}),(0,i.jsx)("div",{className:b.styleItem,onClick:function(){ln(!0),$("硬装")},children:(null===(p=L[0])||void 0===p?void 0:p.title3)&&(null===(h=L[0])||void 0===h?void 0:h.img3)?(0,i.jsxs)("div",{className:"item",children:[(0,i.jsx)("img",{src:null===(m=L[0])||void 0===m?void 0:m.img3,alt:""}),(0,i.jsx)("div",{className:"title",children:v("硬装")}),(0,i.jsxs)("div",{className:"rightitem",children:[(0,i.jsx)("span",{children:null===(x=L[0])||void 0===x?void 0:x.title3}),(0,i.jsx)("span",{className:"seriesStyle",children:null===(g=L[0])||void 0===g?void 0:g.hardseriesStyle})]})]}):(0,i.jsxs)("span",{className:"add",children:[(0,i.jsx)(j.A,{style:{color:"#5B5E60",fontSize:"14px",marginRight:"6px"},type:"icon-anzhuangInstall"}),v("添加硬装风格")]})})]}),(0,i.jsx)("div",{className:b.materialInfo,children:R.map((function(n){return(0,i.jsxs)("div",{className:"itemInfo",children:[(0,i.jsxs)("div",{className:"header",children:[(0,i.jsxs)("span",{className:"title",onClick:function(){return cn(n.label)},children:[n.label,tn[n.label]]}),tn[n.label]?(0,i.jsx)(j.A,{type:"icon-a-fangxiangxia",style:{fontSize:16,color:"#959598"},onClick:function(){return cn(n.label)}}):(0,i.jsx)(j.A,{type:"icon-a-fangxiangyou",style:{fontSize:16,color:"#959598"},onClick:function(){return cn(n.label)}})]}),tn[n.label]&&(0,i.jsx)("div",{className:"itemList",children:n.figureList.map((function(n,e){var t,r,o;return(0,i.jsxs)("div",{className:"item",onClick:function(){var e;q(!0),z.homeStore.setSelectEntity(n.figure_element.furnitureEntity),nn(n.figure_element),(null==n||null===(e=n.figure_element)||void 0===e?void 0:e.haveMatchedMaterial())||I.A.error(v("当前套系缺失".concat(null==n?void 0:n.figure_element.sub_category,"素材，请联系管理员补全"))),(null==n?void 0:n.figure_element.haveMatchedMaterial())&&!(null==n?void 0:n.figure_element.checkIsMatchedSizeSuitable())&&I.A.warning(v("超出目标尺寸范围"))},children:[(null==n?void 0:n.figure_element.haveMatchedMaterial())&&!(null==n?void 0:n.figure_element.checkIsMatchedSizeSuitable())&&(0,i.jsx)(j.A,{className:b.warn_icon,type:"icon-a-tianchongFace"}),!(null==n||null===(t=n.figure_element)||void 0===t?void 0:t.haveMatchedMaterial())&&(0,i.jsx)(j.A,{type:"icon-a-tianchongFace-1",className:"redIcon"}),(0,i.jsx)("img",{src:n.image_path,alt:""}),(0,i.jsx)("div",{children:n.centerTitle}),(0,i.jsx)("div",{style:{color:"#959598",marginTop:"4px"},children:n.bottomTitle}),(null==n||null===(r=n.figure_element)||void 0===r?void 0:r.haveMatchedMaterial())&&(0,i.jsx)(j.A,{className:b.lock_icon,type:(null===(o=n.figure_element)||void 0===o?void 0:o.locked)?"icon-suoding1":"icon-jiesuo1",onClick:function(e){var t;n.room&&(null===(t=n.room)||void 0===t?void 0:t.locked)||n.figure_element&&(n.figure_element.locked=!n.figure_element.locked,S.nb.emit(N.U.RoomMaterialsUpdated,!0),S.nb.instance.update(),e.stopPropagation(),nn(n.figure_element))}})]},e)}))})]},n.label)}))}),an&&(0,i.jsx)("div",{className:b.visible,onClick:function(){ln(!1)},children:(0,i.jsx)("div",{className:"".concat(b.serialsInfo," ").concat(an?"show":""),onClick:function(n){return n.stopPropagation()},children:(0,i.jsx)(C.A,{type:V})})}),(0,i.jsxs)("div",{className:"".concat(b.sideVisible," ").concat(X?b.slideIn:b.slideOut),children:[(0,i.jsxs)("div",{className:"sideTopInfo",children:[(0,i.jsx)("div",{children:"模型位信息"}),(0,i.jsx)("div",{children:(0,i.jsx)(j.A,{type:"icon-icon",style:{color:"#5B5E60"},onClick:function(){q(!1)}})})]}),(0,i.jsx)(A.A,{selectedFigureElement:Q})]})]})}))},83657:function(n,e,t){t.d(e,{$T:function(){return F},Kw:function(){return P},Ay:function(){return T}});var i=t(13274),r=t(61643),o=t(81639);function a(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function l(){var n=a(["\n      /* position: fixed;\n      top: 0px;\n      background-color: #fff;\n      width: 100%;\n      height: 56px;\n\n      display: flex;\n      padding: 0 16px;\n      justify-content: space-between;\n      align-items: center;\n      z-index: 9;\n      max-width: 1174px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 46px;\n      } */\n    "]);return l=function(){return n},n}function c(){var n=a(["\n      position: fixed;\n      top: 12px;\n      left: 12px;\n      color: #282828;\n      font-size: 16px;\n      font-weight: 600;\n      @media screen and (max-width: 450px) { // 手机宽度\n        font-size: 12px;\n      }\n      z-index: 9;\n    "]);return c=function(){return n},n}function s(){var n=a(["\n      position:absolute;\n      right:0;\n      z-index:2;\n      padding-right:10px;\n      font-size:14px;\n      line-height:40px;\n      color:#333;\n    "]);return s=function(){return n},n}function u(){var n=a(["\n      width:100%;\n      font-size:16px;\n      line-height:40px;\n      text-align:center;\n    "]);return u=function(){return n},n}var d=(0,o.rU)((function(n){var e=n.css;return{navigation:e(l()),backBtn:e(c()),forwardBtn:e(s()),schemeNameSpan:e(u())}})),f=t(15696),p=t(41594),h=t(27347);function m(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function x(){var n=m(["\n      width: 180px;\n      position: fixed;\n      right: 12px;\n      top: 12px;\n      z-index: 9;\n      .ant-segmented\n      {\n        /* background-color: #EAEBEA; */\n        /* color: #282828 !important; */\n        @media screen and (max-width: 450px) {\n          height: 28px;\n        }\n      }\n      .ant-segmented-item-label\n      {\n        @media screen and (max-width: 450px) {\n          height: 28px;\n          line-height: 28px !important;\n          font-size: 12px !important;\n        }\n      }\n    "]);return x=function(){return n},n}function g(){var n=m(["\n      right: 50px;\n    "]);return g=function(){return n},n}function v(){var n=m(["\n      width: 50px;\n      text-align:center;\n      line-height:40px;\n      font-size:16px;\n      color : #777;\n      background-color: #fff;\n      border: 1px solid #fff;\n      &.active {\n        background: #147FFA;\n        color: #fff;\n      }\n\n    "]);return v=function(){return n},n}var b=(0,o.rU)((function(n){var e=n.css;return{root:e(x()),mobile_root:e(g()),state_btn:e(v())}})),y=t(7224),w=t(71195),j=t(70524),S=t(88934),_=t(67869),k=t(10371);function C(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function I(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return C(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return C(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var A=(0,f.observer)((function(){var n=(0,r.B)().t,e=b().styles,t=(0,j.P)(),o=I((0,p.useState)(t.homeStore.viewMode||"2D"),2),a=o[0],l=o[1],c=h.nb.instance.layout_container,s=h.nb.instance.scene3D;(0,p.useEffect)((function(){if(h.nb.emit_M(y.z.showLight3DViewer,!0),"2D"===a)s&&s.stopRender(),h.nb.emit_M(y.z.showLight3DViewer,!1),t.homeStore.setViewMode("2D");else if("3D"===a)h.nb.emit_M(y.z.showLight3DViewer,!0),s.setCemeraMode(k.I5.Perspective),t.homeStore.setViewMode("3D"),"SingleRoom"===c._drawing_layer_mode&&h.nb.DispatchEvent(h.n0.leaveSingleRoomLayout,{}),s&&s.startRender();else if("3D_FirstPerson"===a){s.setCemeraMode(k.I5.FirstPerson);var n,e,i,r=t.homeStore.roomEntities.reduce((function(n,e){return n?e._area>n._area?e:n:e}),null);if(r)s.setCenter((null==r||null===(n=r._main_rect)||void 0===n?void 0:n.rect_center)||new _.Pq0(0,0,0)),s.update();else s.setCenter((null===(i=t.homeStore.roomEntities[0])||void 0===i||null===(e=i._main_rect)||void 0===e?void 0:e.rect_center)||new _.Pq0(0,0,0));t.homeStore.setViewMode(a),s&&s.startRender()}h.nb.emit_M(S.U.Scene3DUpdated,!0)}),[a]);var u=[{value:"2D",label:n("2D")},{value:"3D_FirstPerson",label:n("漫游")},{value:"3D",label:n("鸟瞰")}];return(0,i.jsx)("div",{className:e.root,children:(0,i.jsx)(w.A,{value:a,onChange:function(n){l(n)},block:!0,size:"middle",options:u})})})),N=t(87248),z=t(23825),E=t(57235),O=t(76330);function M(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function D(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return M(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return M(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var P=function(n){return n.Default="Default",n.HouseSearch="HouseSearch",n.HuaweiDemo="HuaweiDemo",n}({}),F="NavigationEvent",T=(0,f.observer)((function(n){var e=(0,r.B)().t,t=d().styles,o=h.nb.instance.layout_container,a=D((0,p.useState)(!0),2),l=(a[0],a[1]),c=D((0,p.useState)(!0),2),s=(c[0],c[1]);return(0,p.useEffect)((function(){h.nb.instance&&(h.nb.instance.updateSlot("TopMenu_RedoableSlot",{ui_name:"RedoDisabled",target:h.nb.instance,callback:function(n){l(!n)}}),h.nb.instance.updateSlot("TopMenu_UndoableSlot",{ui_name:"UndoDisabled",target:h.nb.instance,callback:function(n){s(!n)}})),h.nb.instance.connect_obj(E.n.signalRedoable,h.nb.instance,"TopMenu_RedoableSlot"),h.nb.instance.connect_obj(E.n.signalUndoable,h.nb.instance,"TopMenu_UndoableSlot"),h.nb.on(F,(function(n){n||(n="Default")}))}),[]),(0,i.jsxs)("div",{className:t.navigation,children:[!z.Ic&&(0,i.jsx)("div",{className:t.backBtn,onClick:function(){0==o._room_entities.length?window.location.href=z.O9:(h.nb.DispatchEvent(h.n0.autoSave,null),N.A.loading(e("保存中...")),setTimeout((function(){N.A.destroy(),window.location.href=z.O9}),1500))},children:(0,i.jsx)(O.A,{type:"icon-zhuye",style:{fontSize:"18px",marginRight:"4px"}})}),(0,i.jsx)(A,{})]})}))},84749:function(n,e,t){var i=t(81639);function r(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function o(){var n=r(["\n      display: flex;\n      justify-content: start;\n      align-items: center;\n      border-radius: 50px;\n      background: #fff;\n      border: 1px solid #FFFFFF;\n      box-shadow: 0px 6px 20px 0px #0000001E;\n      position: fixed;\n      left: 50%;\n      z-index: 9;\n      transform: translateX(-50%);\n      overflow:hidden;\n      top: 60px; \n      left: 50%; \n       flex-direction: row; \n      .topLine{\n        width: 40px;\n        height: 2px;\n        background: #E0E1E1;\n        position: absolute;\n        top: 5px;\n        left: 50%;\n        transform: translateX(-50%);\n        border-radius: 10px;\n      }\n    "]);return o=function(){return n},n}function a(){var n=r(["\n      opacity: 1;\n    "]);return a=function(){return n},n}function l(){var n=r(["\n      opacity: 0;\n    "]);return l=function(){return n},n}function c(){var n=r(["\n      width: 56px;\n      height: 60px;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n      color: #282828;\n      font-size: 12px;\n      padding: 4px 0px;\n      margin: 0 8px;\n      position: relative;\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 30px !important;\n        height: 44px !important;\n        margin: 0 4px !important;\n        font-size: 10px !important;\n        .label {\n          width:20px;\n        }\n      }\n      @media screen and (orientation: landscape) {\n        width: 48px;\n        font-size: 11px;\n      }\n      div{\n        margin: 0px 0px;\n      }\n      .divider\n      {\n        position: absolute;\n        left: -5px;\n        height: 100%;\n        border-left: 1px #E0E1E1 solid;\n        width: 1px;\n      }\n    "]);return c=function(){return n},n}e.A=(0,i.rU)((function(n){var e=n.css;return{root:e(o()),show:e(a()),hide:e(l()),btnInfo:e(c())}}))},93491:function(n,e,t){t.d(e,{A:function(){return M}});var i=t(13274),r=t(41594);function o(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function a(){var n=o(["\n      background: #fff;\n      height: 500px;\n      padding: 0 16px;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100);\n        padding: 0 12px;\n      }\n      .ant-segmented\n      {\n        background-color: #EAEBEA;\n        color: #282828 !important;\n      }\n    "]);return a=function(){return n},n}function l(){var n=o(["\n        display: flex;\n        justify-content: space-between;\n        margin-top: 20px;\n        padding: 0 24px;\n        @media screen and (orientation: landscape) {\n          margin: 12px 0px;\n          padding: 0 0px;\n        }\n        .info\n        {\n          display: flex;\n          img{\n            width: 72px;\n            height: 72px;\n            border-radius: 4px;\n            margin-right: 16px;\n            @media screen and (orientation: landscape) {\n              width: 80px;\n              height: 80px;\n              margin-right: 12px;\n            }\n          }\n        }\n         .sizeInfo\n         {\n          padding: 8px 0px;\n          @media screen and (orientation: landscape) {\n            padding: 0px 0px;\n          }\n            .size\n            {\n              color: #5B5E60;\n              margin-top: 4px;\n              @media screen and (orientation: landscape) {\n                margin-top: 8px;\n                font-size: 10px;\n              }\n            }\n         } \n      "]);return l=function(){return n},n}function c(){var n=o(["\n      margin: 20px 0 14px 0px;\n      font-size: 14px;\n      font-weight: 600;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      @media screen and (orientation: landscape) {\n        margin: 0px 0 8px 0px;\n      }\n    "]);return c=function(){return n},n}function s(){var n=o(["\n      display: flex;\n      flex-wrap: wrap;\n      overflow-y: scroll;\n      max-height: calc(var(--vh, 1vh) * 100 - 240px);\n      margin-top: 10px;\n      align-items: flex-start;\n       /* 隐藏滚动条 */\n      &::-webkit-scrollbar {\n          display: none; /* 隐藏滚动条 */\n      }\n      \n      /* 对于 Firefox */\n      scrollbar-width: none; /* 隐藏滚动条 */\n      -ms-overflow-style: none; /* IE 和 Edge */\n\n      \n    "]);return s=function(){return n},n}function u(){var n=o(["\n      text-align: center;\n      padding: 20px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      margin: 0 auto;\n    "]);return u=function(){return n},n}function d(){var n=o(["\n      overflow: hidden;\n      width: 104px;\n      margin: 6px 12px 0 12px;\n      text-align: center;\n      @media screen and (max-width: 800px){\n         width: 122px;\n      }\n      @media screen and (max-width: 450px){\n         width: 106px;\n      }\n      @media screen and (max-width: 400px){\n         width: 94px;\n      }\n      @media screen and (orientation: landscape) {\n        margin: 6px 6px 0 6px;\n        width: 88px;\n        font-size: 10px;\n        text-align: left;\n      }\n      img{\n        width: 100%;\n        aspect-ratio: 1 / 1;\n        border-radius: 4px;\n        background-color: #eaeaea;\n        border-radius: 8px;\n      }\n    "]);return d=function(){return n},n}function f(){var n=o(["\n    \n    "]);return f=function(){return n},n}function p(){var n=o(["\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      margin-top: 4px;\n    "]);return p=function(){return n},n}function h(){var n=o(["\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      margin: 0 auto;\n      .emptyImg{\n        width: 120px;\n        height: 120px;\n        margin-bottom: 12px;\n      }\n      span{\n        color: #5B5E60;\n      }\n    "]);return h=function(){return n},n}var m=(0,t(81639).rU)((function(n){var e=n.css;return{root:e(a()),topInfo:e(l()),divider:e(c()),goodsInfo:e(s()),loading:e(u()),goodsItem:e(d()),selectIcon:e(f()),sizeInfo:e(p()),noData:e(h())}})),x=t(27347),g=t(15696),v=t(70524),b=t(61643),y=t(42322),w=t(71195),j=t(52898),S=t(31033),_=t(23825),k=t(63038),C=t(44186);function I(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=n[t];return i}function A(n,e,t,i,r,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(i,r)}function N(n){return function(){var e=this,t=arguments;return new Promise((function(i,r){var o=n.apply(e,t);function a(n){A(o,i,r,a,l,"next",n)}function l(n){A(o,i,r,a,l,"throw",n)}a(void 0)}))}}function z(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function E(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var i,r,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(i=t.next()).done)&&(o.push(i.value),!e||o.length!==e);a=!0);}catch(n){l=!0,r=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw r}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return I(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return I(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(n,e){var t,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,i&&(r=2&l[0]?i.return:l[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,l[1])).done)return r;switch(i=0,r&&(l=[2&l[0],r.value]),l[0]){case 0:case 1:r=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,i=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!r||l[1]>r[0]&&l[1]<r[3])){o.label=l[1];break}if(6===l[0]&&o.label<r[1]){o.label=r[1],r=l;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(l);break}r[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],i=0}finally{t=r=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var M=(0,g.observer)((function(n){var e=n.selectedFigureElement,o=(0,v.P)(),a=(0,b.B)().t,l=m().styles,c=E((0,r.useState)(null==e?void 0:e._candidate_materials),2),s=c[0],u=c[1],d=E((0,r.useState)("套系素材"),2),f=d[0],p=d[1],h=E((0,r.useState)(!1),2),g=h[0],I=h[1],A=(0,r.useRef)(null);(0,r.useEffect)((function(){e&&u(null==e?void 0:e._candidate_materials),p("套系素材")}),[e]);return(0,r.useEffect)((function(){!function(n){N((function(){var t,i,r;return O(this,(function(o){switch(o.label){case 0:return"套系素材"!==n?[3,1]:((null==e?void 0:e._candidate_materials)&&(null==e?void 0:e._candidate_materials.length)>0?u(null==e?void 0:e._candidate_materials):u([]),[3,3]);case 1:return I(!0),[4,(0,S.t5)({categoryId:"",current:1,designMaterialId:null==e||null===(t=e._matched_material)||void 0===t?void 0:t.modelId,size:50,tagIds:[]})];case 2:(i=o.sent()).success&&i.data?(r=i.data.materials.records.map((function(n){return{imageUrl:_.L4+n.imagePath+"?x-oss-process=image/resize,m_fixed,w_120,h_120",name:n.materialName,materialId:n.materialId}})),u(r)):u([]),I(!1),o.label=3;case 3:return[2]}}))}))()}(f)}),[f]),(0,i.jsx)("div",{className:l.root,children:e&&(0,i.jsxs)(i.Fragment,{children:[e&&(0,i.jsx)("div",{className:l.topInfo,children:(0,i.jsxs)("div",{className:"info",children:[(0,i.jsx)("div",{children:(0,i.jsx)("img",{src:e._matched_material.imageUrl||e.image_path,alt:""})}),(0,i.jsxs)("div",{className:"sizeInfo",children:[(0,i.jsx)("div",{children:e._matched_material.name}),(0,i.jsxs)("div",{className:"size",children:[a("图元尺寸"),"：",Math.round(e.rect._w),"*",Math.round(e.rect._h)]}),(0,i.jsxs)("div",{className:"size",children:[a("模型尺寸"),"：",Math.round(e._matched_material.length),"*",Math.round(e._matched_material.width),"*",Math.round(e._matched_material.height)]})]})]})}),(0,i.jsxs)("div",{className:l.divider,children:[(0,i.jsx)("div",{children:a("可用素材")}),(0,i.jsx)("div",{children:["衣柜","玄关柜","餐边柜"].some((function(n){var t;return null==e||null===(t=e.sub_category)||void 0===t?void 0:t.includes(n)}))&&!o.userStore.aihouse&&"C00002170"!==o.userStore.userInfo.tenantId&&(0,i.jsx)(y.A,{style:{marginLeft:10},type:"primary",size:"small",onClick:function(){A.current.onModal()},children:a("AI搭柜")})})]}),(0,i.jsx)(w.A,{block:!0,value:f,options:[a("套系素材"),a("云素材")],onChange:function(n){p(n)}}),(0,i.jsx)("div",{className:l.goodsInfo,children:g?(0,i.jsxs)("div",{className:l.loading,children:[(0,i.jsx)(j.A,{size:"large"})," "]}):s&&s.length>0?s.map((function(n,t){return(0,i.jsxs)("div",{className:l.goodsItem,onClick:function(){return N((function(){var i,r,a,l,c,s,u,d,p;return O(this,(function(h){switch(h.label){case 0:return e.locked?[2]:(o.designStore.setSelectedIndex(t),"套系素材"!==f?[3,1]:(x.nb.DispatchEvent(x.n0.ReplaceMaterial,n),[3,4]));case 1:return a=null,[4,(0,S.Y2)({materialIds:null==n?void 0:n.materialId})];case 2:return(null==(l=h.sent())||null===(r=l.result)||void 0===r||null===(i=r.result)||void 0===i?void 0:i[0])&&(a=null==l||null===(s=l.result)||void 0===s||null===(c=s.result)||void 0===c?void 0:c[0]),[4,(0,k.h)(n.materialId)];case 3:u=h.sent(),a&&(d={modelId:a.MaterialId,imageUrl:n.imageUrl.startsWith("https://")?n.imageUrl:_.L4+n.imageUrl,name:a.MaterialName,originalLength:a.PICLength,originalWidth:a.PICWidth,originalHeight:a.PICHeight,length:a.PICLength,width:a.PICWidth,height:a.PICHeight,modelLoc:e.modelLoc,modelFlag:a.ModelFlag.toString(),topViewImage:u,figureElement:e},p=function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},i=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),i.forEach((function(e){z(n,e,t[e])}))}return n}({},n,d),x.nb.DispatchEvent(x.n0.ReplaceMaterial,p)),h.label=4;case 4:return[2]}}))}))()},children:[t===o.designStore.selectedIndex&&(0,i.jsx)("div",{className:l.selectIcon}),(0,i.jsx)("img",{src:n.imageUrl,alt:""}),(0,i.jsx)("div",{className:l.sizeInfo,children:n.name}),(null==n?void 0:n.length)?(0,i.jsx)("div",{className:l.sizeInfo,style:{color:"#959598"},children:Math.round(null==n?void 0:n.length)+"*"+Math.round(null==n?void 0:n.width)+"*"+Math.round(null==n?void 0:n.height)}):null]},t)})):(0,i.jsxs)("div",{className:l.noData,children:[(0,i.jsx)("img",{className:"emptyImg",src:t(78793),alt:""}),(0,i.jsx)("span",{children:a("暂无可用素材")})]})}),(0,i.jsx)(C.A,{onParams:function(){},selectedFigureElement:e,ref:A})]})})}))}}]);