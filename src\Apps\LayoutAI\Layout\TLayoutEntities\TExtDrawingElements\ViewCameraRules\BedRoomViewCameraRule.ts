// src/Apps/LayoutAI/Services/ViewCameraRules/SofaViewCameraRule.ts
import { ZRect } from "@/Apps/LayoutAI/ZPolygon/ZRect";
import { BaseViewCameraRule } from "./BaseViewCameraRule";
import { TRoomEntity } from "../../TRoomEntity";
import { TViewCameraEntity } from "../TViewCameraEntity";
import { Matrix4, PerspectiveCamera, Vector3, Vector3Like, Vector4 } from "three";
import { TRoomShape } from "../../../TRoomShape";
import { compareNames } from "@/Apps/LayoutAI/Utils/basic_utils";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TBaseGroupEntity } from "../../TBaseGroupEntity";
import { TFurnitureEntity } from "../../TFurnitureEntity";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { TWindowDoorEntity } from "../../TWinDoorEntity";
import { EdgeProp_3DVisible, MinHallwayWidth } from "../../../IRoomInterface";
import { ZEdge } from "@/Apps/LayoutAI/ZPolygon/ZEdge";
export class BedRoomViewCameraRule extends BaseViewCameraRule {
    constructor() {
        super();
    }
    // 计算床视角
    static  generateViewCamera(bed_element: TFigureElement, room_entity: TRoomEntity, prefix: string = "", options: { back_edge_only?: boolean, no_focus_mode?: boolean, enableHideFurnitures?: boolean } = {}): TViewCameraEntity[] {
        if(!bed_element) return [];
        let view_cameras: TViewCameraEntity[] = [];
        let bed_rect = bed_element?.matched_rect || bed_element?.rect;
     
        // 创建视角
        const createViewCamera = (camera_rect: ZRect, point: Vector3) => {
            let view_camera = new TViewCameraEntity();
            view_camera.rect.copy(camera_rect);
            view_camera._room_entity = room_entity;
            view_camera.name = prefix;
            view_camera._is_focus_mode = options.no_focus_mode;
            view_camera._view_center = point.clone();
            view_cameras.push(view_camera);
            view_camera._main_rect = bed_rect;
            view_camera.fov = 75;
            view_camera.hideFurnitures = this.hideFurnitures;
        }
        /**
         * 视角生成规则，沿着床的四周生成视角，距离1800mm
         */
        for(let edge of bed_rect.edges)
        {
            if(edge.center.distanceTo(bed_rect.backEdge.center) < 10) continue;
            let point = edge.unprojectEdge2d({x: edge.length / 2, y: 1600});
            let bed_camera_rect = new ZRect(500, 500);
            bed_camera_rect.nor = edge.nor.clone().negate();
            bed_camera_rect.rect_center = point.clone();
            bed_camera_rect.zval = 1150;
            createViewCamera(bed_camera_rect, point);
        }

        
        // 大卧室才加斜对角视角
        if(room_entity._area > 10)
        {
            let fromEdge = this.updateTargetWallEdge(room_entity._room_poly.edges, bed_rect);
            let pointP = null;
            const origin = bed_rect.backEdge.center; // 坐标原点O (backEdge的中心点)
            const angle = 115; // 视角角度（从背靠边X轴顺时针计算）
            const distance = 3400; // 相机距离原点的距离
            // 1. 将角度转换为方向向量（在床的局部2D坐标系中）
            const angleRad = Math.PI * angle / 180;
            // 这里我们传入dir2D作为参数，表示从原点出发的方向
            const left_pointP = bed_rect.backEdge.unprojectEdge2d({
                x: (bed_rect.backEdge.length / 2) - Math.abs((Math.cos(angleRad))*distance),
                y: -Math.abs(Math.sin(angleRad)) * distance 
            });
            const rigth_pointP = bed_rect.backEdge.unprojectEdge2d({
                x: (bed_rect.backEdge.length / 2) + Math.abs((Math.cos(angleRad))*distance),
                y: -Math.abs(Math.sin(angleRad)) * distance
            });
            let left_len = fromEdge.projectEdge2d(left_pointP).x;
            let right_len = fromEdge.projectEdge2d(rigth_pointP).x;
            pointP = (left_len < 0 || left_len > fromEdge.length) ? rigth_pointP : left_pointP;
            // // 3. 计算从原点到edgeStartPoint的方向向量
            const direction3D = pointP.clone().sub(origin).normalize();
            // 创建视角
            let bed_camera_rect = new ZRect(500, 500);
            bed_camera_rect.nor = direction3D.negate();
            bed_camera_rect.rect_center = pointP.clone();
            bed_camera_rect.zval = 1150;
            createViewCamera(bed_camera_rect, pointP);
        }

        return view_cameras;
    }

    /**
     * 视角显示，隐藏家具规则
     * 
     */
    static hideFurnitures(view_camera: TViewCameraEntity, room_entity: TRoomEntity, options: {hide_wall?: boolean})
    {
        const WallEdge3DVisible = EdgeProp_3DVisible;
        
        const hide_furniture = (entity: TFurnitureEntity) => {
            // 空间内的素材遮挡隐藏，主要是考虑相机前800mm前的遮挡
            let f_rect = entity.rect;
            let v_rect = view_camera.rect;
            // 忽略自己
            if(entity.rect.rect_center.distanceTo(view_camera?._main_rect?.rect_center) < 10) return;
            // 过滤主要隐藏的家具
            if(view_camera._hide_names.includes(entity.category))
            {
                entity._mesh3d.visible = false;
                return;
            };
            let main_camera_dist = null;
            if (view_camera.view_center) {
                main_camera_dist = view_camera?._main_rect.rect_center.clone().sub(view_camera.rect.rect_center).length();
            }
            let pp = f_rect.project(view_camera.rect.rect_center);
            if(Math.abs(pp.y) < main_camera_dist && compareNames([entity.category], ["窗帘",'电视','背景墙']) && Math.abs(view_camera.rect.nor.dot(f_rect.nor)) > 0.9)
            {
                entity._mesh3d.visible = false;
            }
            if(Math.abs(pp.y) < main_camera_dist && (entity.height > 2000 || entity.pos_z > 1500) && compareNames([entity.category], ["柜"]) && Math.abs(view_camera.rect.nor.dot(f_rect.nor)) > 0.9)
            {
                entity._mesh3d.visible = false;
            }
            let new_rect = view_camera.rect.clone();
            new_rect._w = 1000;
            new_rect._h = 900;
            new_rect.rect_center = view_camera.rect.rect_center.clone();
            // 相机周围的家具
            if(new_rect.intersect_rect(f_rect))
            {
                entity._mesh3d.visible = false;
            }

            
        }
        let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
            container._furniture_entities.forEach((entity) => {
                let furniture_entity = entity;
                if (furniture_entity) {
                    // 公共的隐藏家具方法
                    const common_hide_furniture = (furniture_entity: TFurnitureEntity) => {
                        if(!furniture_entity?._mesh3d) return;
                        if(!view_camera._room_entity._room_poly.containsPoint(furniture_entity.rect.rect_center))
                        {
                            furniture_entity._mesh3d.visible = false;
                        } else 
                        {
                            furniture_entity._mesh3d.visible = true;
                            hide_furniture(furniture_entity);
                        }
                    }

                    if(furniture_entity instanceof TBaseGroupEntity)
                    {
                        furniture_entity.combination_entitys.forEach((sub_entity)=> {
                            common_hide_furniture(sub_entity);

                        });
                    } else 
                    {
                        common_hide_furniture(furniture_entity);
                    }
                }
            })
            if(container._room_entities && container._room_entities.length > 0)
            {
                container._room_entities.forEach((room_entity)=>{
                    if(room_entity.decoration_elements)
                    {
                        room_entity.decoration_elements.forEach((ele)=>{
                            let f_rect = ele.rect;
                            if(view_camera && Math.abs(view_camera.rect.nor.dot(f_rect.nor)) < 0.1)
                            {
                                return;
                            }
                            let distance = 600;
                            if (view_camera.view_center) {
                                distance = view_camera?._main_rect.rect_center.clone().sub(view_camera.rect.rect_center).length();
                            }
                            let pp = f_rect.project(view_camera.rect.rect_center);
                            if(Math.abs(pp.y) < distance && ele?._solid_mesh3D)
                            {
                                ele._solid_mesh3D.visible = false;
                            }
                        })
                    }
                })
            }
            // 去除一些遮挡视角的窗户
            if(container._window_entities)
            {
                container._window_entities.forEach((door_entity) => {
                    let door_rect = door_entity.rect;
                    if (door_rect && door_entity._mesh3d) {
                        if(view_camera && Math.abs(view_camera.rect.nor.dot(door_rect.nor)) < 0.1)
                        {
                            let new_rect = view_camera.rect.clone();
                            new_rect._w = 1000;
                            new_rect._h = 900;
                            new_rect.rect_center = view_camera.rect.rect_center.clone();
                            if(new_rect.intersect_rect(door_rect))
                            {
                                door_entity._mesh3d.visible = false;
                            }
                            return;
                        }
                        door_entity._mesh3d.visible = true;
                        let distance = 600;
                        if (view_camera.view_center) {
                            distance = view_camera?._main_rect.rect_center.clone().sub(view_camera.rect.rect_center).length();
                        }
                        let pp = door_rect.project(view_camera.rect.rect_center);
                        if(Math.abs(pp.y) < distance && (Math.abs(pp.x) <= (door_rect.length / 2) + 500))
                        {
                            door_entity._mesh3d.visible = false;
                        }
                    }
                })
            }

            // 去除一些遮挡视角的墙
            if(container._wall_entities)
            {
                container._wall_entities.forEach((wall_entity)=>{
                    let wall_rect = wall_entity.rect;
                    if (wall_rect && wall_entity._mesh3d) {
                        if(view_camera && Math.abs(view_camera.rect.nor.dot(wall_rect.nor)) < 0.1)
                        {
                        
                            // // 和相机法向垂直的侧边墙
                            // if(wall_entity.rect.frontEdge.islayOn(view_camera.rect.rightEdge, 99999))
                            // {
                            //     wall_entity._mesh3d.visible = false;
                            // }
                            // let pp = view_camera.rect.project(wall_rect.rect_center);
                            // if((Math.abs(pp.x) <= (view_camera.rect.length / 2) + 500))
                            // {
                            //     wall_entity._mesh3d.visible = false;
                            // }
                            let new_rect = view_camera.rect.clone();
                            new_rect._w = 1000;
                            new_rect._h = 900;
                            new_rect.rect_center = view_camera.rect.rect_center.clone();
                            if(new_rect.intersect_rect(wall_rect))
                            {
                                wall_entity._mesh3d.visible = false;
                            }

                            // 思路要调整一下，根据视角对着主家居的对边，然后根据layon去计算有没有重叠，如果有，就设置为false
                            if(view_camera._main_rect)
                            {
                                for(let edge of view_camera._main_rect.edges)
                                {
                                    if(view_camera.rect.nor.dot(edge.nor) < -0.9)
                                    {
                                        if(Math.abs(wall_entity.rect.nor.dot(edge.nor)) < 0.1 && edge.islayOn(wall_rect.leftEdge, 99999))
                                        {
                                            wall_entity._mesh3d.visible = false;
                                        }
                                        if(Math.abs(wall_entity.rect.nor.dot(edge.nor)) > 0.9 && edge.islayOn(wall_rect.frontEdge, 99999))
                                        {
                                            wall_entity._mesh3d.visible = false;
                                        }
                                    }
                                }
                            }
                            return;
                        }
                        let distance = 600;
                        if (view_camera.view_center) {
                            distance = view_camera._main_rect.rect_center.clone().sub(view_camera.rect.rect_center).length();
                        }
                        // 规则1，适用于平行或垂直的情况
                        if(Math.abs(view_camera.rect.nor.dot(new Vector3(0, 1, 0))) < 0.01 || Math.abs(view_camera.rect.nor.dot(new Vector3(0, 1, 0))) > 0.99)
                        {
                            let pp = wall_rect.project(view_camera.rect.rect_center);
                            // 允许在墙的两侧各500mm范围内隐藏
                            if(Math.abs(pp.y) < distance && (Math.abs(pp.x) <= (wall_rect.length / 2) + 800))
                            {
                                wall_entity._mesh3d.visible = false;
                            }
                        }
                        else
                        {
                            let main_pp = wall_rect.project(view_camera._main_rect.rect_center);
                            let camrea_pp = wall_rect.project(view_camera.rect.rect_center);
                            if(Math.abs(camrea_pp.y) < Math.abs(main_pp.y))
                            {
                                wall_entity._mesh3d.visible = false;
                            }
                        }

                    }
                })
            }


            let pos = view_camera.rect.rect_center;
            let nor = view_camera.rect.nor;

            let t_view_rect = new ZRect(MinHallwayWidth, 800);
            t_view_rect.nor = nor;
            t_view_rect.rect_center = pos;
            if (view_camera.view_center) {
                let yy = t_view_rect.project(view_camera.view_center).y;
                t_view_rect._h = yy;
                t_view_rect.updateRect();
            }

            container._room_entities.forEach((room_entity) => {

                let poly = room_entity._room_poly;
                poly.edges.forEach((edge) => {

                    if (edge._attached_elements[WallEdge3DVisible] !== undefined) {
                        delete edge._attached_elements[WallEdge3DVisible];
                    }

                    let e_rect = new ZRect(edge.length, 10);
                    e_rect.nor = edge.nor;
                    e_rect.rect_center = edge.center;

                    // if (t_view_rect.containsPoly(e_rect)) {
                    //     edge._attached_elements[WallEdge3DVisible] = false;
                    // }
                    let has_intps = e_rect.intersect_polygons([t_view_rect]);
                    if (has_intps && has_intps.length > 0) {
                        edge._attached_elements[WallEdge3DVisible] = false;

                        // if (e_rect.checkSameNormal(t_view_rect.nor)) {
                        // }
                        // else {

                        // }
                    }

                });

                // 如果是聚焦模式，应该隐藏视角看过去的内墙
                if(view_camera?._room_entity?._room_poly && !view_camera._room_entity._room_poly.containsPoint(view_camera.rect.rect_center))
                {
                    // 隐藏看过去的InnerWall
                    if(room_entity && room_entity._mesh3d)
                    {
                        let wall_mesh = room_entity._mesh3d.children.find((child)=>child.name.endsWith("InnerWall"));
                        if(wall_mesh)
                        {
                            wall_mesh.visible = false;
                        }
                    }
                }

                // room_entity._updateInnerWallMesh();
            })
    }

    static updateTargetWallEdge = (edges: ZEdge[] , main_rect: ZRect) => {
        let nor = main_rect.frontEdge.nor;
        let m_dist = 999999999;
        let center = main_rect.frontEdge.center;
        let _target_wall_edge = null; 
        for(let edge of edges)
        {
            if((edge.nor.dot(nor) > -0.9)) continue;
            let pp = edge.projectEdge2d(center);
            if(pp.x < 0 || pp.x > edge.length) continue;
            if(!_target_wall_edge || pp.y < m_dist)
            {
                _target_wall_edge = edge;
                m_dist = pp.y;
             
            }


        }
        return _target_wall_edge
    }
}