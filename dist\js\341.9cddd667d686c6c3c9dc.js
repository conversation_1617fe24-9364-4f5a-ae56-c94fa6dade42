"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[341],{66628:function(e,n,t){t.r(n),t.d(n,{default:function(){return Te}});var r=t(13274),i=t(61643),o=t(66910),a=t(15696),s=t(79324),c=t(87248),l=t(42322),u=t(41594),d=t(27347),f=t(2021),h=t(70524),p=t(88934),m=t(78644),b=t(83657),x=t(23825),g=t(8636),y=t(90503),v=t(19356),w=t(79888),S=t(22640),j=t(84872),_=t(58567),I=t(78613),O=t(56697),A=t(42751),C=t(63577),D=t(90112),E=t(51010),k=t(75670),P=t(35700),N=t(99030),z=t(17365),M=t(61928),L=t(81639);function F(e,n){return n||(n=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(n)}}))}function R(){var e=F(["\n      position:fixed;\n      left:0;\n      bottom:0;\n      height: calc(var(--vh, 1vh) * 100);\n      width:100%;\n      z-index: 999;\n      background: #fff;\n      .slide-enter {\n        transform: translateX(-100%);\n        opacity: 0;\n      }\n\n      .slide-enter-active {\n        transform: translateX(0);\n        opacity: 1;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n      .slide-exit {\n        transform: translateX(0);\n        opacity: 1;\n      }\n\n      .slide-exit-active {\n        transform: translateX(100%);\n        opacity: 0;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n\n\n      .slide-reverse-enter {\n        transform: translateX(100%);\n        opacity: 0;\n      }\n\n      .slide-reverse-enter-active {\n        transform: translateX(0);\n        opacity: 1;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n      .slide-reverse-exit {\n        transform: translateX(0);\n        opacity: 1;\n      }\n\n      .slide-reverse-exit-active {\n        transform: translateX(-100%);\n        opacity: 0;\n        transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;\n      }\n\n      .upload_hx\n      {\n        position: fixed;\n        right: 25px;\n        bottom: 60%;\n      }\n    "]);return R=function(){return e},e}function U(){var e=F(["\n      padding: 0px 10px;\n      height: 100%;\n      /* .right_btns\n      {\n        position: fixed;\n        right: 25px;\n        top: 25px;\n      } */\n\n    "]);return U=function(){return e},e}function B(){var e=F(["\n      width: 100%;\n      height: 100%;\n    "]);return B=function(){return e},e}function T(){var e=F(["\n      padding: 0px 40px;\n      height: calc(var(--vh, 1vh) * 100 - 170px);\n      margin-top: 16px;\n      overflow-y: scroll;\n      ::-webkit-scrollbar\n      {\n        display: none;\n      }\n      .demandLabel\n        {\n          font-weight: 600;\n          font-size: 16px;\n          color: #000;\n          margin-bottom: 8px;\n          margin-top: 20px;\n        }\n      .demandItem\n      {\n       \n        .tabRoot\n        {\n          display: flex;\n          flex-wrap: wrap;\n        }\n      }\n      .demandtab\n      {\n        display: flex;\n        width: 100px;\n        height: 32px;\n        padding: 4px 16px;\n        justify-content: center;\n        align-items: center;\n        border-radius: 6px;\n        background: #F2F3F4;\n        margin-right: 12px;\n        margin-bottom: 12px;\n      }\n      .selected\n      {\n        border-radius: 6px;\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n        box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.08);\n        color: #fff;\n      }\n    "]);return T=function(){return e},e}function H(){var e=F(["\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n    "]);return H=function(){return e},e}function Z(){var e=F(["\n      width: 100%;\n      height: 100%;\n    "]);return Z=function(){return e},e}function V(){var e=F(["\n      display: flex;\n      padding: 40px 40px 0px 40px;\n      justify-content: space-between;\n      align-items: center;\n\n      .title{\n        font-size: 24px;\n        font-weight: 600;\n        display: flex;\n        align-items: center;\n        .back {\n          width: 28px;\n          height: 28px;\n          border-radius: 6px;\n          background: #E9EBEB;\n          padding: 4px;\n          margin-right: 8px;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 14px;\n        }\n      }\n\n      .mySchemeButton{\n        display: flex;\n        align-items:center;\n        font-weight: 600;\n        position: fixed;\n        right: 120px;\n        font-size: 11px;\n      }\n      .myAtlasButton{\n        display: flex;\n        align-items:center;\n        font-weight: 600;\n        position: fixed;\n        right: 20px;\n        font-size: 11px;\n      }\n    "]);return V=function(){return e},e}function X(){var e=F(["\n      position: fixed;\n      bottom: 0;\n      left: 0;\n      width: 100%;\n      height: 88px;\n      background: #fff;\n      display: flex;\n      align-items: center;\n      padding: 20px 60px;\n      justify-content: space-between;\n      .ant-btn\n      {\n        width: 160px;\n        height: 48px;\n        border-radius: 24px;\n      }\n      .rotate\n      {\n        font-size: 16px;\n        color: #5B5E60;\n      }\n    "]);return X=function(){return e},e}function K(){var e=F(["\n      display: flex;\n      flex-wrap: wrap;\n      box-sizing: border-box;\n      margin-top: 20px;\n    "]);return K=function(){return e},e}function W(){var e=F(["\n      width: calc(20% - 10px);\n      height: auto;\n      padding: 2px;\n      box-sizing: border-box;\n      position: relative;\n      margin-right: 10px;\n      @media (max-width: 800px) {\n        width: calc(33.33% - 10px);\n      }\n      img{\n        width: 100%;\n        aspect-ratio: 5/3;\n      }\n    "]);return W=function(){return e},e}function q(){var e=F(["\n      padding: 0 5px;\n      "]);return q=function(){return e},e}function Y(){var e=F(["\n      color: #282828;\n      font-family: PingFang SC;\n      font-weight: medium;\n      font-size: 14px;\n      line-height: 22px;\n      letter-spacing: 0px;\n      text-align: left;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      width: 100%;\n      margin-top: 5px;\n      font-weight: 600;\n      display: flex;\n      justify-content: space-between;\n      padding: 0 10px;\n      .ant-rate\n      {\n        color: #FFAA00;\n        font-size: 16px !important;\n        .ant-rate-star:not(:last-child)\n        {\n          margin-inline-end: 3px;\n        }\n      }\n    "]);return Y=function(){return e},e}function $(){var e=F(["\n      color: #6C7175;\n      font-family: PingFang SC;\n      font-weight: regular;\n      font-size: 12px;\n      line-height: 20px;\n      letter-spacing: 0px;\n      text-align: left;\n      display: flex;\n      margin-top: 5px;\n    "]);return $=function(){return e},e}var G=(0,L.rU)((function(e){e.token;var n=e.css;return{enterPage:n(R()),selectHx:n(U()),hxRoot:n(B()),selectDemand:n(T()),styleTitle:n(H()),demandRoot:n(Z()),hxHeader:n(V()),bottom:n(X()),container_listInfo:n(K()),container_list:n(W()),textInfo:n(q()),container_title:n(Y()),container_desc:n($())}})),J=t(71195),Q=t(46396),ee=t(76330),ne=t(53837),te=t(93413),re=t(61307),ie=t(9221),oe=t(97082);function ae(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function se(e,n,t,r,i,o,a){try{var s=e[o](a),c=s.value}catch(e){return void t(e)}s.done?n(c):Promise.resolve(c).then(r,i)}function ce(e){return function(){var n=this,t=arguments;return new Promise((function(r,i){var o=e.apply(n,t);function a(e){se(o,r,i,a,s,"next",e)}function s(e){se(o,r,i,a,s,"throw",e)}a(void 0)}))}}function le(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function ue(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),r.forEach((function(n){le(e,n,t[n])}))}return e}function de(e,n){return n=null!=n?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):function(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})),e}function fe(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,i,o=[],a=!0,s=!1;try{for(t=t.call(e);!(a=(r=t.next()).done)&&(o.push(r.value),!n||o.length!==n);a=!0);}catch(e){s=!0,i=e}finally{try{a||null==t.return||t.return()}finally{if(s)throw i}}return o}}(e,n)||pe(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function he(e){return function(e){if(Array.isArray(e))return ae(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||pe(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pe(e,n){if(e){if("string"==typeof e)return ae(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?ae(e,n):void 0}}function me(e,n){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(c){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&s[0]?r.return:s[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,s[1])).done)return i;switch(r=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,r=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){o.label=s[1];break}if(6===s[0]&&o.label<i[1]){o.label=i[1],i=s;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(s);break}i[2]&&o.ops.pop(),o.trys.pop();continue}s=n.call(e,o)}catch(e){s=[6,e],r=0}finally{t=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}var be=(0,a.observer)((function(){var e,n,t,o=(0,u.useRef)(null),a=(0,i.B)().t,s=G().styles,f=fe((0,u.useState)(null),2),m=f[0],b=f[1],g=fe((0,u.useState)([]),2),y=g[0],v=g[1],w=fe((0,u.useState)([{label:"居住人口",multiple:!1,tabList:[{label:"单身独居",selected:!1},{label:"二人世界",selected:!1},{label:"三口之家",selected:!1},{label:"四口之家",selected:!1},{label:"多代同堂",selected:!1}]},{label:"房屋类型",multiple:!1,tabList:[{label:"毛坯房",selected:!1},{label:"精装修",selected:!1},{label:"旧房改造",selected:!1}]},{label:"功能需求",multiple:!0,tabList:[{label:"聚会",selected:!1},{label:"品茗",selected:!1},{label:"健身",selected:!1},{label:"绿植",selected:!1},{label:"收纳",selected:!1},{label:"梳妆",selected:!1},{label:"休闲",selected:!1},{label:"西厨",selected:!1},{label:"宠物",selected:!1},{label:"办公",selected:!1},{label:"适老",selected:!1},{label:"孩童",selected:!1}]},{label:"装修预算",multiple:!1,tabList:[{label:"10万以下",selected:!1},{label:"10-20万",selected:!1},{label:"20-50万",selected:!1},{label:"50万以上",selected:!1}]}]),2),S=w[0],j=w[1],_=(0,h.P)(),I=fe((0,u.useState)({orderBy:"sort asc",ruleType:(null===(e=_.userStore.userInfo)||void 0===e?void 0:e.isFactory)?2:1,pageSize:50,pageIndex:1,schemeKeyWord:"",ruleKeyWord:"",spaceName:null,schemeStyleId:"",ruleStyleId:"",queryType:2}),2),O=I[0],A=I[1],C=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return ce((function(){var n,t;return me(this,(function(r){switch(r.label){case 0:return[4,(0,re.Ic)(O)];case 1:return(null==(n=r.sent())?void 0:n.result)?(t=Array.isArray(y)&&y.length>0&&O.pageIndex>1?he(y).concat(he((null==n?void 0:n.result)||[])):(null==n?void 0:n.result)||[],v(t),e&&b(t[0])):v([]),[2]}}))}))()},D=fe((0,u.useState)(0),2),E=D[0],k=D[1];(0,u.useEffect)((function(){d.nb.on(p.U.xmlSchemeLoaded,(function(e){if("Finish"===e.mode&&m){d.nb.DispatchEvent(d.n0.SeriesSampleSelected,{series:m,scope:{soft:!0,hard:!0,cabinet:!0,remaining:!1}});var n="";S.forEach((function(e){e.tabList.forEach((function(e){e.selected&&(n+=e.label+",")}))})),n=n.slice(0,-1),d.nb.instance.layout_container._funcRequire=n,b(null),_.trialStore.setHouseData(null),d.nb.off(p.U.xmlSchemeLoaded)}}))}),[m,S]);var P=function(){"sideToolbar"===_.homeStore.showEnterPage.source&&_.homeStore.showEnterPage.show?_.homeStore.setShowEnterPage({show:!1,source:""}):0===E?(M.K.exitSDK(),window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*"),window.location.href=x.O9):1===E&&k(0)};(0,u.useEffect)((function(){"HouseId"!==x.Zx&&"CopyingBase64"!==x.Zx||k(1),C(!0)}),[]),(0,u.useEffect)((function(){1===E&&C(!1)}),[O]),(0,u.useEffect)((function(){var e,n;0===E&&(null===(n=_.trialStore)||void 0===n||null===(e=n.houseData)||void 0===e?void 0:e.id)&&(k(1),_.homeStore.setImgBase64(null))}),[null===(t=_.trialStore)||void 0===t||null===(n=t.houseData)||void 0===n?void 0:n.num]);return(0,r.jsxs)("div",{className:s.enterPage,children:[(0,r.jsxs)("div",{className:s.hxHeader,children:[(0,r.jsxs)("div",{className:"title",children:[(0,r.jsx)("div",{className:"back",onClick:P,children:(0,r.jsx)(ee.A,{type:"icon-line_left"})}),(0,r.jsx)(Q.If,{condition:0===E,children:(0,r.jsx)("span",{children:a("找户型")})}),(0,r.jsx)(Q.If,{condition:1===E,children:(0,r.jsx)("span",{children:a("选需求")})})]}),(0,r.jsxs)(l.A,{type:"primary",className:"mySchemeButton",color:"orange",variant:"filled",onClick:function(){_.homeStore.setShowMySchemeList(!0)},children:[(0,r.jsx)(oe.In,{iconClass:"iconwenjianjia",style:{fontSize:"12px"}}),(0,r.jsx)("div",{style:{color:"rgba(0, 0, 0, 0.8)"},children:"我的方案"})]}),(0,r.jsxs)(l.A,{type:"primary",className:"myAtlasButton",color:"purple",variant:"filled",onClick:function(){_.homeStore.setShowAtlas(!0)},children:[(0,r.jsx)(oe.In,{iconClass:"icontuce1",style:{fontSize:"12px"}}),(0,r.jsx)("div",{style:{color:"rgba(0, 0, 0, 0.8)"},children:"我的图册"})]})]}),(0,r.jsxs)("div",{className:"upload_hx",onClick:function(){return ce((function(){var e;return me(this,(function(n){switch(n.label){case 0:return[4,(0,ie.L7)("image/*").catch((function(e){return null}))];case 1:return(e=n.sent()).content?(_.homeStore.setImgBase64(e.content),c.A.success(a("上传户型图成功")),k(1)):c.A.warning(a("上传户型图失败")),[2]}}))}))()},style:{display:0===E?"block":"none"},children:[(0,r.jsx)("img",{style:{marginTop:"20px",width:"50px",height:"auto"},src:"./static/icons/upload.svg",alt:""}),(0,r.jsx)("div",{className:"upload_title",children:"上传户型"})]}),(0,r.jsx)(te.A,{in:0===E,timeout:300,classNames:1===E?"slide-reverse":"slide",mountOnEnter:!0,appear:!0,style:{display:0===E?"block":"none"},children:(0,r.jsx)("div",{className:s.selectHx,children:(0,r.jsx)("div",{className:s.hxRoot,children:(0,r.jsx)(ne.A,{})})})}),(0,r.jsx)(te.A,{in:1===E,timeout:300,classNames:0===E?"slide":"slide-reverse",mountOnEnter:!0,appear:!0,style:{display:1===E?"block":"none"},children:function(){var e,n;return(0,r.jsxs)("div",{className:s.selectDemand,children:[S.map((function(e,n){return(0,r.jsxs)("div",{className:"demandItem",children:[(0,r.jsx)("div",{className:"demandLabel",children:e.label},n),(0,r.jsx)("div",{className:"tabRoot",children:e.tabList.map((function(n,t){return(0,r.jsx)("div",{onClick:function(){return t=n,r=e.label,i=S.map((function(e){return e.label===r?de(ue({},e),{tabList:e.tabList.map((function(n){return e.multiple?de(ue({},n),{selected:n.label===t.label?!n.selected:n.selected}):de(ue({},n),{selected:n.label===t.label})}))}):e})),void j(i);var t,r,i},className:"demandtab"+(n.selected?" selected":""),children:n.label},t)}))})]})})),(0,r.jsxs)("div",{className:s.styleTitle,children:[(0,r.jsxs)("div",{className:"demandLabel",children:[a("风格偏好"),(0,r.jsxs)("span",{style:{color:"#959598",fontSize:"12px"},children:["（",a("必选"),"）"]})]}),(0,r.jsx)(J.A,{onChange:function(e){return ce((function(){return me(this,(function(n){return A((function(n){return de(ue({},n),{pageIndex:1,ruleType:"平台"===e?1:2})})),b(null),[2]}))}))()},defaultValue:(null===(e=_.userStore.userInfo)||void 0===e?void 0:e.isFactory)?"企业":"平台",options:["平台","企业"]})]}),(0,r.jsx)("div",{className:"".concat(s.container_listInfo),ref:o,children:(0,r.jsx)(r.Fragment,{children:null==y||null===(n=y.map)||void 0===n?void 0:n.call(y,(function(e,n){return(0,r.jsx)("div",{className:s.container_list,onClick:function(){b(e)},children:(0,r.jsxs)("div",{style:{border:(null==m?void 0:m.ruleId)===e.ruleId?"2px solid #9242FB":"2px solid #fff",borderRadius:"8px",overflow:"hidden"},children:[(0,r.jsx)("img",{src:"".concat(e.thumbnail,"?x-oss-process=image/resize,m_fixed,h_218,w_318"),alt:""}),(0,r.jsx)("div",{className:s.container_title,title:e.seedSchemeName||e.ruleName,children:a(e.seedSchemeName)||a(e.ruleName)})]})},"series_"+n)}))})})]})}()}),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Q.If,{condition:0===E}),(0,r.jsx)(Q.If,{condition:1===E,children:(0,r.jsxs)("div",{className:s.bottom,children:[(0,r.jsx)("div",{className:"bottomLeft",children:(0,r.jsxs)("div",{className:"rotate",onClick:function(){b(null),j(S.map((function(e){return de(ue({},e),{tabList:e.tabList.map((function(e){return de(ue({},e),{selected:!1})}))})})))},children:[(0,r.jsx)(ee.A,{type:"icon-rotate",style:{marginRight:"5px"}}),a("重置选项")]})}),(0,r.jsxs)("div",{className:"bottomRight",children:[(0,r.jsx)(l.A,{style:{marginRight:"10px"},onClick:function(){return k(0)},color:"default",variant:"filled",children:a("上一步")}),(0,r.jsx)(l.A,{style:{background:"linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%)",color:"#fff"},onClick:function(){m?(k(0),_.homeStore.setShowEnterPage({show:!1,source:""}),_.homeStore.img_base64?d.nb.DispatchEvent(d.n0.LoadImitateImageFile,_.homeStore.img_base64):_.trialStore.houseData.id&&d.nb.DispatchEvent(d.n0.PostBuildingId,{id:_.trialStore.houseData.id,name:""})):c.A.warning(a("请选择风格偏好"))},color:"default",variant:"filled",children:a("下一步")})]})]})})]})]})}));function xe(e,n){return n||(n=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(n)}}))}function ge(){var e=xe(["\n      background-color: #fff;\n      border-radius: 8px !important;\n      position: relative;\n      .swj-baseComponent-Containersbox-title\n      {\n        background-color: #fff !important;\n      }\n    "]);return ge=function(){return e},e}function ye(){var e=xe(["\n      .ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected > .ant-table-cell\n      {\n        background-color: #fff !important;\n      }\n    "]);return ye=function(){return e},e}function ve(){var e=xe(['\n      color: #282828;\n      font-family: "PingFang SC";\n      font-size: 16px;\n      font-style: normal;\n      font-weight: 600;\n    ']);return ve=function(){return e},e}function we(){var e=xe(["\n      display: flex;\n      flex-direction: row;\n      gap: 10px;\n      margin: 16px 0px;\n      color: #282828;\n      .tabItem\n      {\n        display: flex;\n        width: 64px;\n        height: 28px;\n        padding: 0px 0px;\n        justify-content: center;\n        align-items: center;\n        border-radius: 6px;\n      }\n      .selected\n      {\n        background: #F2F3F4;\n        font-weight: 600;\n      }\n    "]);return we=function(){return e},e}function Se(){var e=xe(["\n      .ant-table-thead .ant-table-selection-column .ant-checkbox-wrapper\n      {\n         display: none;\n      }\n      .ant-table-thead .ant-table-cell\n      {\n        background: #F2F3F4;\n      }\n      .ant-table-container\n      {\n        border: 2px solid #F2F3F4;\n      }\n    "]);return Se=function(){return e},e}function je(){var e=xe(["\n      display: flex;\n      flex-direction: row;\n      justify-content: space-between;\n      align-items: center;\n      position: absolute;\n      bottom: 0;\n      width: 100%;\n      padding: 16px;\n      background-color: #fff;\n      width: 95%;\n      font-size: 16px;\n    "]);return je=function(){return e},e}function _e(){var e=xe(["\n      font-size: 16px;\n      font-weight: 600;\n    "]);return _e=function(){return e},e}var Ie=(0,L.rU)((function(e){var n=e.css;return{panelContainer:n(ge()),content:n(ye()),title:n(ve()),tab:n(we()),table:n(Se()),bottom:n(je()),bottomRight:n(_e())}})),Oe=t(39991).A,Ae=t(68586);function Ce(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function De(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function Ee(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})))),r.forEach((function(n){De(e,n,t[n])}))}return e}function ke(e,n){return n=null!=n?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):function(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})),e}function Pe(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,i,o=[],a=!0,s=!1;try{for(t=t.call(e);!(a=(r=t.next()).done)&&(o.push(r.value),!n||o.length!==n);a=!0);}catch(e){s=!0,i=e}finally{try{a||null==t.return||t.return()}finally{if(s)throw i}}return o}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return Ce(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Ce(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Ne=(0,a.observer)((function(){var e,n,t,o,a=(0,h.P)(),s=((0,i.B)().t,Ie().styles),c=Pe((0,u.useState)("checkbox"),2),l=c[0],f=(c[1],Pe((0,u.useState)([]),2)),p=f[0],m=f[1],b=Pe((0,u.useState)([]),2),x=b[0],g=b[1],y=Pe((0,u.useState)("延米"),2),v=y[0],w=y[1],S=Pe((0,u.useState)(0),2),j=S[0],_=S[1],I=Pe((0,u.useState)([{name:"定制柜",selected:!1},{name:"橱柜",selected:!1},{name:"卫阳",selected:!1}]),2),O=I[0],A=I[1],C=[{title:"类型",dataIndex:"name"},{title:"尺寸",dataIndex:"size"},{title:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",cursor:"pointer"},onClick:function(){w("延米"===v?"投影面积":"延米")},children:[(0,r.jsx)(Q.If,{condition:null===(e=O.find((function(e){return"定制柜"===e.name})))||void 0===e?void 0:e.selected,children:(0,r.jsx)(Q.al,{children:"投影面积"})}),(0,r.jsx)(Q.If,{condition:null===(n=O.find((function(e){return"橱柜"===e.name})))||void 0===n?void 0:n.selected,children:(0,r.jsx)(Q.al,{children:"延米"})}),(0,r.jsx)(Q.If,{condition:null===(t=O.find((function(e){return"卫阳"===e.name})))||void 0===t?void 0:t.selected,children:(0,r.jsxs)(Q.al,{children:[(0,r.jsx)(ee.A,{type:"icon-change_logo"}),v]})})]}),dataIndex:"meter",render:function(e,n){return(0,r.jsx)("span",{children:"延米"===v?n.meter:n.area})}},{title:"空间",dataIndex:"space",hidden:null===(o=O.find((function(e){return"橱柜"===e.name})))||void 0===o?void 0:o.selected}],D={onChange:function(e,n){m(e)},getCheckboxProps:function(e){return{disabled:"Disabled User"===e.name,name:e.name}}};return(0,u.useEffect)((function(){var e,n=O.find((function(e){return e.selected})),t=d.nb.instance.layout_container;if(g([]),0!=(null==t||null===(e=t._room_entities)||void 0===e?void 0:e.length)){if("橱柜"===(null==n?void 0:n.name)){var r;w("延米");var i=t._room_entities.find((function(e){return"厨房"===e.name}))._room;if(i&&(null===(r=i._furniture_list)||void 0===r?void 0:r.length)>0){var o=[];i._furniture_list.forEach((function(e){if(e.category.endsWith("柜")){var n=e.matched_rect?e.matched_rect:e.rect;o.push({key:e.uuid,name:e.category,size:"".concat(n._w,"*").concat(n._h,"*").concat(n.rect_center_3d.z||e.height),meter:"".concat((n._w/1e3).toFixed(2),"m"),space:i.name,area:"".concat((n._w*(n.rect_center_3d.z||e.height)/1e6).toFixed(2),"m²")})}})),g(o)}}if("卫阳"===(null==n?void 0:n.name)){var a=t._room_entities.filter((function(e){if(e.name.includes("卫生间")||e.name.includes("阳台"))return e}));if(a&&a.length>0){var s=[];a.forEach((function(e){var n;(null===(n=e._room._furniture_list)||void 0===n?void 0:n.length)>0&&e._room._furniture_list.forEach((function(e){if(e.category.endsWith("柜")){var n=e.matched_rect?e.matched_rect:e.rect;s.push({key:e.uuid,name:e.category,size:"".concat(Math.round(n._w),"*").concat(Math.round(n._h),"*").concat(Math.round(n.rect_center_3d.z||e.height)),meter:"".concat((n._w/1e3).toFixed(2),"m"),space:e._room.name,area:"".concat((n._w*Math.round(n.rect_center_3d.z||e.height)/1e6).toFixed(2),"m²")})}}))})),g(s)}}if("定制柜"===(null==n?void 0:n.name)){w("投影面积");var c=t._room_entities.filter((function(e){if(!e.name.includes("卫生间")&&!e.name.includes("阳台")&&!e.name.includes("厨房"))return e}));if(c&&c.length>0){var l=[];c.forEach((function(e){var n;(null===(n=e._room._furniture_list)||void 0===n?void 0:n.length)>0&&e._room._furniture_list.forEach((function(e){e.category.endsWith("柜")&&l.push({key:e.uuid,name:e.category,size:"".concat(Math.round(e.rect._w),"*").concat(Math.round(e.rect._h),"*").concat(Math.round(e.rect.rect_center_3d.z||e.height)),meter:"".concat((e.rect._w/1e3).toFixed(2),"m"),space:e._room.name,area:"".concat((e.rect._w*Math.round(e.rect.rect_center_3d.z||e.height)/1e6).toFixed(2),"m²")})}))})),g(l)}}m([]),_(j+1)}}),[O]),(0,u.useEffect)((function(){a.homeStore.showCabinetCompute&&A(O.map((function(e,n){return ke(Ee({},e),{selected:0===n})})))}),[a.homeStore.showCabinetCompute]),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(Q.If,{condition:a.homeStore.showCabinetCompute,children:(0,r.jsx)(oe._w,{center:!0,height:446,width:600,showHeader:!0,showCloseInContainerbox:!1,title:"基本算量",className:s.panelContainer,onClose:function(){a.homeStore.setShowCabinetCompute(!1)},draggable:!0,children:(0,r.jsxs)("div",{style:{padding:"0 20px 20px 20px"},className:s.content,children:[(0,r.jsx)("div",{className:s.tab,children:O.map((function(e,n){return(0,r.jsx)("div",{onClick:function(){A(O.map((function(e,t){return ke(Ee({},e),{selected:t===n})})))},className:"tabItem"+(e.selected?" selected":""),children:e.name},n)}))}),(0,r.jsx)(Oe,{rowSelection:Ee({type:l,columnTitle:"",selectedRowKeys:p},D),columns:C,dataSource:x,pagination:!1,scroll:{y:240},className:s.table},j),(0,r.jsxs)("div",{className:s.bottom,children:[(0,r.jsx)("div",{children:(0,r.jsx)(Ae.A,{checked:p.length===x.length,indeterminate:p.length>0&&p.length<x.length,onChange:function(e){e.target.checked?m(x.map((function(e){return e.key}))):m([])},children:"全选"})}),(0,r.jsx)("div",{className:s.bottomRight,children:(0,r.jsxs)(Q.If,{condition:"延米"===v,children:[(0,r.jsxs)(Q.al,{children:["共 ",x.filter((function(e){return p.includes(e.key)})).reduce((function(e,n){return e+parseFloat(n.meter)}),0).toFixed(2),"m"]}),(0,r.jsxs)(Q._I,{children:["共 ",x.filter((function(e){return p.includes(e.key)})).reduce((function(e,n){return e+parseFloat(n.area)}),0).toFixed(2),"m²"]})]})})]})]})})})})})),ze=t(67869),Me=t(65640);function Le(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function Fe(e,n,t,r,i,o,a){try{var s=e[o](a),c=s.value}catch(e){return void t(e)}s.done?n(c):Promise.resolve(c).then(r,i)}function Re(e){return function(){var n=this,t=arguments;return new Promise((function(r,i){var o=e.apply(n,t);function a(e){Fe(o,r,i,a,s,"next",e)}function s(e){Fe(o,r,i,a,s,"throw",e)}a(void 0)}))}}function Ue(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,i,o=[],a=!0,s=!1;try{for(t=t.call(e);!(a=(r=t.next()).done)&&(o.push(r.value),!n||o.length!==n);a=!0);}catch(e){s=!0,i=e}finally{try{a||null==t.return||t.return()}finally{if(s)throw i}}return o}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return Le(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Le(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Be(e,n){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(c){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&s[0]?r.return:s[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,s[1])).done)return i;switch(r=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,r=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){o.label=s[1];break}if(6===s[0]&&o.label<i[1]){o.label=i[1],i=s;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(s);break}i[2]&&o.ops.pop(),o.trys.pop();continue}s=n.call(e,o)}catch(e){s=[6,e],r=0}finally{t=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}var Te=(0,a.observer)((function(){var e=(0,i.B)().t,n=(0,o.A)().styles,t=Ue((0,u.useState)(""),2),a=t[0],L=t[1],F=Ue((0,u.useState)(-2),2),R=F[0],U=F[1],B=(0,h.P)(),T=s.A.confirm,H="SaveSchemeProgress",Z=Ue(c.A.useMessage(),2),V=Z[0],X=Z[1],K=Ue((0,u.useState)(!1),2),W=K[0],q=(K[1],Ue((0,u.useState)(!1),2)),Y=q[0],$=q[1],G=Ue((0,u.useState)(null),2),J=G[0],ee=G[1],ne=Ue((0,u.useState)(""),2),te=ne[0],re=ne[1],ie=Ue((0,u.useState)(-2),2),ae=(ie[0],ie[1]),se=Ue((0,u.useState)(0),2),ce=se[0],le=(se[1],(0,u.useRef)());d.nb.UseApp(f.e.AppName),d.nb.instance&&(d.nb.t=e);var ue=function(){d.nb.instance&&(d.nb.instance.bindCanvas(document.getElementById("cad_canvas")),d.nb.instance.update()),de()},de=function(){d.nb.instance&&(d.nb.instance._is_landscape=window.innerWidth>window.innerHeight);B.homeStore.IsLandscape;B.homeStore.setIsLandscape(window.innerWidth>window.innerHeight),document.documentElement.style.setProperty("--vh","".concat(.01*window.innerHeight,"px"))};return(0,u.useEffect)((function(){z.f.updateAliasName(),B.homeStore.setRoomEntites(d.nb.instance.layout_container._room_entities)}),[d.nb.instance.layout_container._room_entities]),(0,u.useEffect)((function(){if(d.nb.instance&&(d.nb.instance._is_website_debug=x.iG),window.addEventListener("resize",ue),ue(),d.nb.instance){var n;if(d.nb.instance.initialized||(!(0,x.fZ)()||"HouseId"!==x.Zx&&"CopyingBase64"!==x.Zx||d.nb.emit(p.U.Initializing,{initializing:!0}),d.nb.instance.init(),d.nb.RunCommand(f.f.AiCadMode),d.nb.instance.prepare().then((function(){Re((function(){var e,n,t;return Be(this,(function(r){switch(r.label){case 0:return x.uN?(e={isDelete:0,pageIndex:1,pageSize:9,keyword:x.uN},[4,j.D.getLayoutSchemeList(e)]):[2];case 1:return n=r.sent(),t=n.layoutSchemeDataList,n.total,t&&(d.nb.DispatchEvent(d.n0.OpenMyLayoutSchemeData,t[0]),d.nb.emit(p.U.OpenHouseSearching,!1)),[2]}}))}))(),Re((function(){var e,n,t,r;return Be(this,(function(i){switch(i.label){case 0:return"HouseId"!==x.Zx?[3,1]:(Re((function(){var e,n,t;return Be(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,(0,_.ZN)("HouseId")];case 1:return e=r.sent(),Me.log(e),n=e.data,(0,x.fZ)()?B.trialStore.houseData.id=n:d.nb.DispatchEvent(d.n0.PostBuildingId,{id:n,name:""}),[3,3];case 2:return t=r.sent(),Me.error("Error loading file:",t),[3,3];case 3:return[2]}}))}))(),[3,9]);case 1:return"DwgBase64"!==x.Zx?[3,2]:(Re((function(){return Be(this,(function(e){try{d.nb.RunCommand(d._I.OpenDwgFilefromWork)}catch(e){Me.error("Error loading file:",e)}return[2]}))}))(),[3,9]);case 2:return"CopyingBase64"!==x.Zx?[3,3]:(Re((function(){var e,n,t;return Be(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,(0,_.ZN)("CopyingBase64")];case 1:return e=r.sent(),n=e.data,B.homeStore.setImgBase64(n),(0,x.fZ)()?Me.log("fileData",n):d.nb.DispatchEvent(d.n0.LoadImitateImageFile,n),[3,3];case 2:return t=r.sent(),Me.error("Error loading file:",t),[3,3];case 3:return[2]}}))}))(),[3,9]);case 3:return"hxcreate"!==x.Zx?[3,6]:x.fW?[4,(0,I.O)({id:x.fW})]:[3,5];case 4:(e=i.sent()).result.contentUrl=e.result.dataUrl,d.nb.DispatchEvent(d.n0.OpenMyLayoutSchemeData,e.result),d.nb.DispatchEvent(d.n0.autoSave,null),i.label=5;case 5:return[3,9];case 6:return"hxedit"!==x.Zx?[3,9]:x.vu?[4,(0,I.O)({id:x.vu})]:[3,8];case 7:(n=i.sent()).success&&n.result&&n.result.dataUrl&&(n.result.contentUrl=n.result.dataUrl,d.nb.DispatchEvent(d.n0.OpenMyLayoutSchemeData,n.result)),i.label=8;case 8:d.nb.instance&&"SingleRoom"==(null===(r=d.nb.instance)||void 0===r||null===(t=r.layout_container)||void 0===t?void 0:t._drawing_layer_mode)&&d.nb.DispatchEvent(d.n0.leaveSingleRoomLayout,{}),d.nb.instance._current_handler_mode=f.f.HouseDesignMode,d.nb.RunCommand(f.f.HouseDesignMode),B.homeStore.setDesignMode(f.f.HouseDesignMode),i.label=9;case 9:return[2]}}))}))(),d.nb.emit(p.U.Initializing,{initializing:!1});var e=d.nb.instance.scene3D;e&&e.stopRender()})),d.nb.instance.bindCanvas(document.getElementById("cad_canvas"))),null===(n=window)||void 0===n?void 0:n.URLSearchParams){var t=new URLSearchParams(window.location.search).get("debug");if(null!==t){var r="1"===t?1:0;localStorage&&(localStorage.setItem("LayoutAI_Debug",String(r)),d.nb.instance._debug_mode=r)}}d.nb.instance.update()}d.nb.on_M("showLight3DViewer","PadMobile",(function(e){e?R<0&&(U(2),B.homeStore.setZIndexOf3DViewer(2),d.nb.emit(N.r.UpdateScene3D,!0)):U(-1)})),d.nb.on(p.U.ShowDreamerPopup,(function(e){B.homeStore.setShowDreamerPopup(e)})),d.nb.on(p.U.LayoutSchemeOpened,(function(e){L(e.name),d.nb.emit(b.$T,b.Kw.Default)})),d.nb.on(p.U.ClearLayout,(function(){T({title:e("清空布局"),content:e("确定清空单空间布局？"),okText:e("确定"),cancelText:e("取消"),onOk:function(){d.nb.DispatchEvent(d.n0.ClearLayout,this)},onCancel:function(){}})})),d.nb.on(p.U.OpenMySchemeList,(function(){B.homeStore.setShowMySchemeList(!0)})),d.nb.on_M(p.U.RoomList,"room_list",(function(e){B.homeStore.setRoomInfos(e)})),d.nb.on(p.U.Room2SeriesSampleRoom,(function(e){B.homeStore.setRoom2SeriesSampleArray(e)})),d.nb.on(p.U.showCustomKeyboard,(function(e){setTimeout((function(){$(!0)}),50),e.input&&(re(e.input.value),ee(e.input))})),d.nb.on_M(p.U.updateAllMaterialScene3D,"padMobile",(function(e){return Re((function(){var n,t,r,i,o,a;return Be(this,(function(s){switch(s.label){case 0:return n=d.nb.instance.layout_container,t=d.nb.instance.scene3D,e&&"3D_FirstPerson"===B.homeStore.viewMode?(d.nb.emit(p.U.ApplySeriesSample,{seriesOpening:!0,title:"更新视角中..."}),r=[],n._room_entities.forEach((function(e){e._view_cameras.forEach((function(e){r.push(e)}))})),i=r.map((function(e){return Re((function(){return Be(this,(function(r){switch(r.label){case 0:return e._perspective_img.src?[3,2]:(t.active_controls.bindViewEntity(e),t.update(),[4,e.updatePerspectiveViewImg(n.painter)]);case 1:r.sent(),r.label=2;case 2:return[2]}}))}))()})),[4,Promise.allSettled(i)]):[3,2];case 1:s.sent(),(o=n._room_entities.reduce((function(e,n){return e?n._area>e._area?n:e:n}),null))?(t.active_controls.bindViewEntity(o._view_cameras[0]),B.homeStore.setCurrentViewCameras(o._view_cameras)):t.setCenter((null==o||null===(a=o._main_rect)||void 0===a?void 0:a.rect_center)||new ze.Pq0(0,0,0)),d.nb.emit(p.U.ApplySeriesSample,{seriesOpening:!1,title:""}),s.label=2;case 2:return[2]}}))}))()})),d.nb.on(p.U.SaveProgress,(function(n){"success"===n.progress?(V.open({key:H,type:"success",content:e("布局方案保存成功"),duration:3,style:{marginTop:"6vh",zIndex:9999}}),"autoExit"===B.homeStore.isAutoExit&&(M.K.exitSDK(),window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*"),window.location.href=x.O9),B.homeStore.setIsAutoExit("")):"fail"===n.progress?V.open({key:H,type:"error",content:e("布局方案保存失败"),duration:3,style:{marginTop:"6vh",zIndex:9999}}):"ongoing"===n.progress&&V.open({key:H,type:"loading",content:e("正在保存布局方案"),duration:3,style:{marginTop:"6vh",zIndex:9999}})}))}),[B.homeStore.isAutoExit]),(0,u.useEffect)((function(){4===B.homeStore.zIndexOf3DViewer?U(2):U(B.homeStore.zIndexOf3DViewer)}),[B.homeStore.zIndexOf3DViewer]),(0,r.jsxs)("div",{className:n.root,children:[(0,r.jsx)(P.A,{updateKey:ce}),(0,r.jsxs)("div",{id:"Canvascontent",className:n.content,children:[(0,r.jsx)("div",{className:"3d_container "+n.canvas3d,style:{zIndex:R},children:(0,r.jsx)(m.A,{defaultViewMode:4})}),(0,r.jsxs)("div",{id:"body_container",className:n.canvas_pannel+" left_panel_layout",children:[(0,r.jsx)("canvas",{id:"cad_canvas",className:"canvas",onMouseEnter:function(){B.homeStore.setIsmoveCanvas(!1)},onMouseLeave:function(){B.homeStore.setIsmoveCanvas(!0)},onTouchStart:function(e){if(2===e.touches.length){var n=e.touches[0].clientX-e.touches[1].clientX,t=e.touches[0].clientY-e.touches[1].clientY,r=Math.sqrt(n*n+t*t);B.homeStore.setInitialDistance(r/B.homeStore.scale)}},onTouchMove:function(e){if(e.stopPropagation(),2!=e.touches[e.touches.length-1].identifier&&2===e.touches.length){var n=e.touches[0].clientX-e.touches[1].clientX,t=e.touches[0].clientY-e.touches[1].clientY,r=Math.sqrt(n*n+t*t)/B.homeStore.initialDistance;r>5?r=5:r<.001&&(r=.001),B.homeStore.setScale(r),d.nb.DispatchEvent(d.n0.scale,r)}},onTouchEnd:function(e){e.touches.length>0&&d.nb.DispatchEvent(d.n0.updateLast_pos,e),B.homeStore.setInitialDistance(null)}}),B.homeStore.designMode===f.f.MeasurScaleMode&&(0,r.jsxs)("div",{className:"canvas_btns",style:{zIndex:999999,marginBottom:"10vh",gap:"20px"},children:[(0,r.jsx)(l.A,{className:"btn",type:"primary",onClick:function(){d.nb.instance&&(d.nb.RunCommand(f.f.AiCadMode),B.homeStore.setDesignMode(f.f.AiCadMode))},children:e("取消")}),(0,r.jsx)(l.A,{className:"btn",type:"primary",onClick:function(){d.nb.instance&&(d.nb.DispatchEvent(d.n0.ConfirmScale,{img_base64:B.homeStore.img_base64}),B.homeStore.setDesignMode(f.f.AiCadMode))},children:e("确定")})]})]})]}),(0,r.jsx)(Q.If,{condition:B.homeStore.showEnterPage.show,children:(0,r.jsx)(be,{})}),(0,r.jsx)(Ne,{}),(0,r.jsx)(y.A,{}),(0,r.jsx)(g.ti,{}),(0,r.jsx)(C.A,{}),B.homeStore.showDreamerPopup&&(0,r.jsx)(S.A,{}),B.homeStore.isSingleRoom&&(0,r.jsx)("div",{className:n.RoomAreaBtns,children:(0,r.jsx)(A.A,{mode:1})}),B.homeStore.showSaveLayoutSchemeDialog.show&&(0,r.jsx)("div",{className:n.overlay,children:(0,r.jsx)(w.A,{schemeName:a||"",closeCb:function(){B.homeStore.setShowSaveLayoutSchemeDialog({show:!1,source:""})},isSaveAs:W})}),(0,r.jsx)(v.A,{schemeNameCb:function(e){L(e)}}),(0,r.jsx)(O.A,{onKeyPress:function(e){J&&(J.value=J.value+e,re(J.value))},onDelete:function(){J&&(J.value=J.value.slice(0,-1),re(J.value))},onConfirm:function(){J&&(d.nb.DispatchEvent(d.n0.DimensionInput,J),$(!1),re(""))},onClose:function(){$(!1),re("")},inputValue:te,isVisible:Y}),B.homeStore.showDreamerPopup&&(0,r.jsx)(S.A,{}),(0,r.jsx)(D.A,{ref:le}),(0,r.jsx)(E.A,{}),B.homeStore.showAtlas&&(0,r.jsx)("div",{className:n.mobile_atlas_container,style:{zIndex:999},children:(0,r.jsx)(k.A,{setZIndexOfMobileAtlas:ae})}),(0,r.jsx)(oe.cq,{channelCode:"Helptips-006"}),X]})}))}}]);