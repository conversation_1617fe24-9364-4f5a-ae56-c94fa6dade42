import {
  cad_file,
  checkIsMobile,
  ENV,
  EnvParams,
  gatewayUrl,
  import_Type,
  is_standalone_website,
  mini_APP,
  mode_type
} from '@/config';
import SunvegaAPI from '@api/clouddesign';
import { Vector3 } from 'three';
import {
  compareNames,
  FileOpenResult,
  GenDateUUid,
  getShortenedFileName,
  I_MouseEvent,
  openFileInput,
  uploadImageToOss
} from '../../LayoutAI/Utils/basic_utils';
import { ZRect } from '../../LayoutAI/ZPolygon/ZRect';
import { LayoutAI_App, LayoutAI_Commands } from '../../LayoutAI_App';
import { AI2BaseModeHandler } from '../AI2BaseModeHandler';
import { AI2DesignBasicModes, AI2DesignManager } from '../AI2DesignManager';
import { AddFurnitureHandler } from './SubHandlers/CadEditSubHandlers/AddFurnitureHandler';

import { LayoutAI_Events } from '@/Apps/LayoutAI_App';
import { EventName } from '../../EventSystem';

import { EzdxfParser } from '@/Apps/LayoutAI/AICadData/EzDxfParser';
import { I_SwjXmlScheme } from '@/Apps/LayoutAI/AICadData/SwjLayoutData';
import { CadDrawingLayerType } from '@/Apps/LayoutAI/Drawing/TDrawingLayer';
import { I_MaterialMatchingItem } from '@/Apps/LayoutAI/Layout/IMaterialInterface';
import {
  AI_PolyTargetType,
  DrawingFigureMode,
  IRoomEntityRealType,
  IRoomEntityType,
  RoomSpaceAreaType
} from '@/Apps/LayoutAI/Layout/IRoomInterface';
import { TLayoutFineTuningManagerToolUtil } from '@/Apps/LayoutAI/Layout/TLayoutFineTuningOperation/TLayoutFineTuningManagerToolUtil';
import { TGroupTemplate } from '@/Apps/LayoutAI/Layout/TLayoutGraph/TGroupTemplate/TGroupTemplate';
import { TMaterialMatcher } from '@/Apps/LayoutAI/Services/MaterialMatching/TMaterialMatcher';
import { TSeriesFurnisher } from '@/Apps/LayoutAI/Services/MaterialMatching/TSeriesFurnisher';
import { TRoom } from '@/Apps/LayoutAI/Layout/TRoom';
import { TBaseEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseEntity';
import { TBaseGroupEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TBaseGroupEntity';
import { TFurnitureEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity';
import { TGroupTemplateEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TGroupTemplateEntity';
import { TRoomEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity';
import { TStructureEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TStructureEntity';
import { TWindowDoorEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TWinDoorEntity';
import { checkRoomNamesCompleteness } from '@/Apps/LayoutAI/Utils/layout_utils';
import { I_PainterTransform } from '@/Apps/LayoutAI/ZPolygon/ZPainter';
import { Arrangeable, FocusMap, MobileFocusMap } from '@/config/enum';
import { clearFiles, loadFile } from '@/IndexDB';
import { reverseTranslation } from '@/locales';
import { g_FigureImagePaths } from '../../LayoutAI/Drawing/FigureImagePaths';
import { T_GroupOperationInfo } from '../../LayoutAI/OperationInfos/Operations/T_GroupOpertaionInfo';
import { T_ReplaceFurnitureLabelOperationInfo } from '../../LayoutAI/OperationInfos/Operations/T_ReplaceFurnitureLabelOperationInfo';
import { T_ReplaceGroupOperationInfo } from '../../LayoutAI/OperationInfos/Operations/T_ReplaceGroupOperationInfo';
import { BasicRoomAILayoutSubHandler } from './SubHandlers/AILayoutSubHandlers/BasicRoomAILayoutSubHandler';
import { KitchenLayoutSubHandler } from './SubHandlers/AILayoutSubHandlers/KitchenLayoutSubHandler';
import { AddSpaceAreaSubHandler } from './SubHandlers/CadEditSubHandlers/AddSpaceAreaSubHandler';
import { CadBaseSubHandler } from './SubHandlers/CadEditSubHandlers/CadBaseSubHandler';
import { CombinationHandler } from './SubHandlers/CadEditSubHandlers/CombinationHandler';
import { DimensionFurnitureHandler } from './SubHandlers/CadEditSubHandlers/DimensionFurnitureHandler';
import { EditSpaceAreaSubHandler } from './SubHandlers/CadEditSubHandlers/EditSpaceAreaSubHandler';
import { MoveFurnitureSubHandler } from './SubHandlers/CadEditSubHandlers/MoveFurnitureSubHandler';
import { MoveWinDoorHandler } from './SubHandlers/CadEditSubHandlers/MoveWinDoorHandler';
import { RotateHandler } from './SubHandlers/CadEditSubHandlers/RotateHandler';
import { RulerModeHandler } from './SubHandlers/CadEditSubHandlers/RulerModeHandler';
import { ScaleFurnitureSubHandler } from './SubHandlers/CadEditSubHandlers/ScaleFurnitureSubHandler';
import { MoveWallSubHandler } from './SubHandlers/HouseDesignSubHandlers/MoveWallSubHandler';
import { T_DimensionDWElement } from '../../LayoutAI/Layout/TransformElements/T_DimensionDWElement';
import { T_DimensionElement } from '../../LayoutAI/Layout/TransformElements/T_DimensionElement';
import { T_MoveElement } from '../../LayoutAI/Layout/TransformElements/T_MoveElement';
import { T_MoveSubAreaElement } from '../../LayoutAI/Layout/TransformElements/T_MoveSubAreaElement';
import { T_MoveWinDoorElement } from '../../LayoutAI/Layout/TransformElements/T_MoveWinDoorElement';
import { T_RotateElement } from '../../LayoutAI/Layout/TransformElements/T_RotateElement';
import { T_ScaleElement } from '../../LayoutAI/Layout/TransformElements/T_ScaleElement';
import { T_UpdateLengthElement } from '../../LayoutAI/Layout/TransformElements/T_UpdateLengthElement';
import { TLayoutParamConfigurationManager } from '@/Apps/LayoutAI/Layout/TLayoutScoreConfigurationTool/TLayoutParamConfigurationManager';
import { TFigureElement } from '@/Apps/LayoutAI/Layout/TFigureElements/TFigureElement';
import { LayoutContainerUtils } from '@/Apps/LayoutAI/Layout/TLayoutEntities/utils/LayoutContainerUtils';
import { LayoutAiCadDataParser } from '@/Apps/LayoutAI/Layout/TLayoutEntities/loader/LayoutAiCadDataParser';
import { TRoomTemplateSaver } from '@/Apps/LayoutAI/Layout/TLayoutEntities/loader/TRoomTemplateSaver';
import { LayoutSchemeXmlJsonParser } from '@/Apps/LayoutAI/Layout/TLayoutEntities/loader/LayoutSchemeXmlJsonParser';
import { trialDataService } from '../Services/TrialDataService';
import { deflate_to_base64_str } from '@/Apps/LayoutAI/Utils/xml_utils';
import { SchemeXmlParseService } from '../../LayoutAI/Services/Basic/SchemeXmlParseService';
import { t } from 'i18next';
import { message } from '@svg/antd';
import { roomSubAreaService } from '../../LayoutAI/Services/Basic/RoomSubAreaService';
import { TPostDecoratesLayout } from '@/Apps/LayoutAI/Layout/TLayoutGraph/TLayoutRelations/TLayoutOptimizer/TPostLayoutDecorates';
import { MaterialService } from '@/Apps/LayoutAI/Services/MaterialMatching/MaterialService';
import { MatchingPostProcesser } from '@/Apps/LayoutAI/Services/MaterialMatching/MatchingPostProcesser';
import { SensorsLogger } from '@/services/SensorsLogger';
import { getKgSchemeListOfPlatform } from '@/pages/Design/components/service';
import { TAppManagerBase } from '@/Apps/AppManagerBase';
import { FigureTopViewer } from '@/Apps/LayoutAI/Scene3D/process/FigureTopViewer';
import { getDesignMaterialByIdsWithOutPlaceheights } from '@/components/NewReplaceProduct/services/material';
import { AI_CadData, I_AI_CadData } from '@/Apps/LayoutAI/AICadData/AI_CadData';
import { T_DeleteElement } from '@/Apps/LayoutAI/Layout/TransformElements/T_DeleteElement';
import { LayoutSchemeService } from '@/Apps/LayoutAI/Services/Basic/LayoutSchemeService';
import { T_TransformElement } from '@/Apps/LayoutAI/Layout/TransformElements/T_TransformElement';
export class AICadEditModeHandler extends AI2BaseModeHandler {
  _src_ai_cad_data: I_AI_CadData;
  protected _cad_data_changed = false;
  protected _test_data_id: number;

  _painter_ts: I_PainterTransform;
  _target_pos: Vector3;
  main_rect: ZRect;

  /**
   *  重置的矩形列表
   */
  protected _reset_furniture_entities: TFurnitureEntity[];

  /**
   *  重置的矩形列表
   */
  protected _reset_windoor_entities: TWindowDoorEntity[];

  constructor(manager: AI2DesignManager, name: string = 'AICadMode') {
    super(manager, name);
    this._src_ai_cad_data = null;
    this._test_data_id = 0;
    this._painter_ts = null;
    this._cad_default_sub_handler = new CadBaseSubHandler(this);

    this._sub_handlers[LayoutAI_Commands.AddFurniture] = new AddFurnitureHandler(this); // 从左侧拖拽图元进来
    this._sub_handlers[LayoutAI_Commands.Transform_Scaling] = new ScaleFurnitureSubHandler(this);
    this._sub_handlers[LayoutAI_Commands.Transform_Moving] = new MoveFurnitureSubHandler(this);
    this._sub_handlers[LayoutAI_Commands.Transform_MovingStruture] = new MoveWinDoorHandler(this); //移动结构件
    this._sub_handlers[LayoutAI_Commands.Transform_MovingWall] = new MoveWallSubHandler(this); //移动结构件

    this._sub_handlers[LayoutAI_Commands.Transform_Dimension] = new DimensionFurnitureHandler(this);
    this._sub_handlers[LayoutAI_Commands.Transform_Combination] = new CombinationHandler(this); //进入组合模式
    this._sub_handlers[LayoutAI_Commands.Transform_Rotate] = new RotateHandler(this); //进入旋转模式
    this._sub_handlers[LayoutAI_Commands.RulerModeHandler] = new RulerModeHandler(this); //进入量尺模式

    this._sub_handlers[LayoutAI_Commands.AI_RoomLayout] = new BasicRoomAILayoutSubHandler(this);
    this._sub_handlers[LayoutAI_Commands.AI_KitchenLayout] = new KitchenLayoutSubHandler(this);

    this._sub_handlers[AddSpaceAreaSubHandler.handler_name] = new AddSpaceAreaSubHandler(this);
    this._sub_handlers[EditSpaceAreaSubHandler.handler_name] = new EditSpaceAreaSubHandler(this);

    this._reset_furniture_entities = [];
    this._transform_elements = [];

    // this._transform_elements.push(new T_ScaleElement(1, -1, this._selected_target, this.manager.layout_container));
    this._transform_elements.push(
      new T_DeleteElement(1, -1, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(1, 0, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_RotateElement(1, 1, this._selected_target, this.manager.layout_container)
    );

    this._transform_elements.push(
      new T_ScaleElement(0, -1, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(0, 1, this._selected_target, this.manager.layout_container)
    );

    this._transform_elements.push(
      new T_ScaleElement(-1, -1, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(-1, 0, this._selected_target, this.manager.layout_container)
    );
    this._transform_elements.push(
      new T_ScaleElement(-1, 1, this._selected_target, this.manager.layout_container)
    );

    this._transform_elements.push(new T_DimensionElement(0, this.manager.layout_container));
    this._transform_elements.push(new T_DimensionElement(1, this.manager.layout_container));
    this._transform_elements.push(new T_DimensionElement(2, this.manager.layout_container));
    this._transform_elements.push(new T_DimensionElement(3, this.manager.layout_container));
    // this._transform_elements.push(new T_RotateElement(this._selected_target, this.manager.layout_container));
    // this._transform_elements.push(new T_UpdateLengthElement(this._selected_target, this.manager.layout_container));

    this._transform_elements.push(new T_DimensionDWElement(this.manager));

    // 后面就把那四个编辑的点给加进去
    this._transform_moving_element = new T_MoveElement(
      this._selected_target,
      this.manager.layout_container
    );
    this._transform_moving_struture_element = new T_MoveWinDoorElement(
      this._selected_target,
      this.manager.layout_container
    );
    this._transform_elements.push(
      new T_MoveSubAreaElement(this._selected_target, this.manager.layout_container)
    );

    this._transform_elements.push(this._transform_moving_element);
    this._transform_elements.push(this._transform_moving_struture_element);
    this._initial_scheme_data = null; // 初始的方案数据
  }

  get ai_cad_data() {
    return this.manager.layout_container?._ai_cad_data;
  }
  set ai_cad_data(data: AI_CadData) {
    LayoutAiCadDataParser.Container = this.manager.layout_container;
    LayoutAiCadDataParser.fromAiCadData(data);
  }

  get candidate_rects() {
    return this._candidate_target_rects;
  }

  get exsorb_rects() {
    return this._exsorb_target_rects;
  }

  get transform_elements() {
    return this._transform_elements;
  }

  get transform_moving_element() {
    return this._transform_moving_element;
  }

  get painter() {
    return this.manager?.painter;
  }

  get furniture_entities() {
    return this.manager.layout_container._furniture_entities;
  }

  set furniture_entities(rects: TFurnitureEntity[]) {
    this.manager.layout_container._furniture_entities = rects;
  }
  get room_list() {
    return this.manager.layout_container._rooms;
  }

  async prepare(load_test_data: boolean = true) {
    // 从工作台跳转过来
    if (EnvParams.id && EnvParams.id.length > 0 && mini_APP) {
      let res = await this.loadRoomTemplate(EnvParams.id, EnvParams.roomTemplate_RoomId);
      if (res) return;
    }

    let hasInputCadData: boolean = cad_file != null && cad_file.length > 0;

    if (is_standalone_website) {
      // 独立站点进来
      if (load_test_data) {
        LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false, title: null });

        await this.prepareTestData();
      }
      LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false, title: null });
      return;
    } else if (import_Type === 'importHouse') {
      // 看看有什么数据要提前载入
      if (hasInputCadData) {
        this.EventSystem.emit(EventName.LayoutSchemeOpened, {
          id: null,
          name: getShortenedFileName(cad_file)
        });
        this.manager.layout_container._layout_scheme_name = getShortenedFileName(cad_file);
        await this.prepareCadData(cad_file);
      } else if (load_test_data) {
        await this.prepareTestData();
      }

      LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false, title: null });
    } else {
      // 3D进来
      await this.prepare3DschemeData();

      /**
       *  后台静默请求即可
       */
      this.computeWholeLayoutSchemeList(false).then(() => {
        if (this.whole_layout_scheme_list && this.whole_layout_scheme_list.length > 0) {
          LayoutAI_App.DispatchEvent(LayoutAI_Events.ClickWholeLayoutScheme, {
            value: this.whole_layout_scheme_list[0],
            index: 0
          });
        }
      });
    }

    if (this.manager.layout_container) {
      // 重置改成方案保存的形式去做
      this._initial_scheme_data = this.manager.layout_container.toXmlSchemeData();
    }
  }
  async prepare3DschemeData() {
    this.logger.log('>>>>>> 开始准备3D方案数据 <<<<<<');
    this.manager.layout_container.focusCenter();

    // 从3D读取数据... ...
    await this.load3DSchemeData();

    if (this.manager.layout_container._rooms.length == 0 && LayoutAI_App.IsDebug) {
      // 房间还是为空的话
      this.manager._load_local_XmlSchemeData();
      this.manager.layout_container.focusCenter();
      this.manager.layout_container.needs_making_wall_xml = true;
      this.EventSystem.emit(EventName.LayoutSchemeOpened, {
        id: this.manager.layout_container._scheme_id,
        name: this.manager.layout_container._layout_scheme_name
      });
    }

    // await this.queryModelRoomsFromServer();
    // 判断空间是否可选
    this.isSelectRoom();
    this.update();
  }
  isSelectRoom() {
    for (let room of this.room_list) {
      // 新增判断是否可点击逻辑
      room.selectable = false;
      Arrangeable.forEach(supportedRoomType => {
        if (room.room_type.indexOf(supportedRoomType) > -1) {
          room.selectable = true;
        }
      });
    }
  }
  async load3DSchemeData(): Promise<void> {
    this.EventSystem.emit(EventName.LayoutSchemeOpening, { id: null, name: '' });
    this.EventSystem.emit(EventName.OnPreparingHandle, {
      opening: true,
      title: '正在读取3D方案数据...'
    });

    let houseStrutureResponse: SunvegaAPI.Core.APIResponse<SunvegaAPI.BasicBiz.HouseApi.GetHouseStructureOutput> =
      null;
    houseStrutureResponse = await SunvegaAPI.BasicBiz.HouseApi.getHouseStructure();

    if (!houseStrutureResponse?.data?.house) {
      this.logger.warn('3D方案数据读取失败');
      this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });
      return;
    }

    if (houseStrutureResponse.data.house.room_list.length > 0) {
      let livingRoomCount: number = 0;
      let bedroomCount: number = 0;
      let kitchenCount: number = 0;
      let bathRoomCount: number = 0;
      let balconyCount: number = 0;
      houseStrutureResponse.data.house.room_list.forEach(room => {
        if (room.name.indexOf('厅') > -1) {
          livingRoomCount++;
        } else if (room.name.indexOf('卧') > -1) {
          bedroomCount++;
        } else if (room.name.indexOf('厨') > -1) {
          kitchenCount++;
        } else if (room.name.indexOf('阳台') > -1) {
          balconyCount++;
        } else if (room.name.indexOf('卫') > -1) {
          bathRoomCount++;
        } else if (room.name.indexOf('书房') > -1) {
          bedroomCount++;
        } else {
          bedroomCount++;
        }
        room.name = reverseTranslation[room.name] || room.name;
        if (room.name == 'Балкон') {
          room.name = '阳台';
        }
        // room.name = TRoom.getOverseasRoomNameByName(room.name);
        if (room.name === '厨卫') {
          room.name = '厨房';
        }
        if (room.name === '客厅') {
          room.name = '客餐厅';
        }
      });
      let roomCountStr: string =
        `${bedroomCount} ` +
        LayoutAI_App.t('室') +
        ` ${livingRoomCount} ` +
        LayoutAI_App.t('厅') +
        ` ${kitchenCount} ` +
        LayoutAI_App.t('厨') +
        ` ${bathRoomCount} ` +
        LayoutAI_App.t(`卫`);
      const layoutSchemeName: string =
        '' + roomCountStr + ' ' + houseStrutureResponse.data.house.area.toFixed(2) + '㎡';
      this.EventSystem.emit(EventName.LayoutSchemeOpened, { id: null, name: layoutSchemeName });
      this.manager.layout_container._layout_scheme_name = layoutSchemeName;
    } else {
      this.EventSystem.emit(EventName.LayoutSchemeOpenFail, { id: null, name: null });
    }

    let houseStruture: SunvegaAPI.BasicBiz.House = (
      houseStrutureResponse.data as SunvegaAPI.BasicBiz.HouseApi.GetHouseStructureOutput
    ).house;
    if (houseStruture.storey_height && houseStruture.storey_height > 0) {
      this.manager.layout_container._storey_height = houseStruture.storey_height;
    }

    await this.manager.layout_container.fetchPublicCategoryForAll3DMaterials(
      houseStruture as any as I_SwjXmlScheme
    );
    // this.printMaterialsFrom3D(this.manager.layout_container._material_id_public_category_map, houseStruture as any as I_SwjXmlScheme);
    this.manager.layout_container.fromXmlSchemeData(houseStruture as any as I_SwjXmlScheme);

    // 暂时不从3D读取数据
    let room =
      this.manager.layout_container._rooms.find(room => room.name == '卧室') ||
      this.manager.layout_container._rooms[0];
    if (room) {
      this.main_rect = ZRect.computeMainRect(room?.room_shape._poly);
      let ww = Math.max(this.main_rect.w, this.main_rect.h);
      let canvasElement = this.painter._canvas;
      let scaleW = canvasElement.width / ww;
      let scaleH = canvasElement.height / ww;
      this.manager.layout_container.focusCenter();
      this.painter._p_sc = (scaleW > scaleH ? scaleH : scaleW) * 0.15;
    }
    this.manager.layout_container.needs_making_wall_xml = false;
    this.manager.layout_container._is_from_3d_scheme = true;

    let model_id_dict = this.manager.layout_container._load_model_material_dict();
    if (model_id_dict) {
      for (let ele of this.manager.layout_container._furniture_entities) {
        if (ele.figure_element) {
          let material_id = ele.figure_element._ex_prop.material_id;
          if (material_id && model_id_dict[material_id]) {
            ele.figure_element.category =
              model_id_dict[material_id][0] || ele.figure_element.category;
            ele.figure_element.sub_category =
              model_id_dict[material_id][1] || ele.figure_element.sub_category;
            ele.figure_element.public_category = ele.figure_element.sub_category;
          }
        }
      }
    }

    this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });

    this.update();
  }

  async prepareCadData(cadFileUrl: string) {
    console.log('CadFileUrl---', cadFileUrl);
    if (cadFileUrl == null || cadFileUrl.length == 0) {
      return;
    }

    let cadDataJsonObject: I_AI_CadData = (await this.parseCadUrl(
      cadFileUrl
    )) as unknown as I_AI_CadData;

    if (cadDataJsonObject) {
      this.painter.p_center = this.ai_cad_data.center;
      this.painter.p_scale = 0.1;
    }
  }

  /**
   *  本地测试数据准备
   */
  async prepareTestData() {
    let has_ezdxf = null;
    if (!has_ezdxf && this.manager.layout_container._room_entities.length == 0) {
      if (this.manager._load_local_XmlSchemeData()) {
        this.manager.layer_CadEzdxfLayer._clean();
        this.manager.layer_CadEzdxfLayer.ezdxf_data = null;

        this.EventSystem.emit(EventName.LayoutSchemeOpened, {
          id: this.manager.layout_container._scheme_id,
          name: this.manager.layout_container._layout_scheme_name
        });

        // this.manager.layout_container.focusCenter();
        if (window.innerWidth < window.innerHeight * 0.8) {
          LayoutContainerUtils.focusCenterByWholeBox(this.manager.layout_container, 0.7);
        } else {
          LayoutContainerUtils.focusCenterByWholeBox(this.manager.layout_container, 0.5);
        }

        this.manager.layout_container.needs_making_wall_xml = true;
        this._cad_default_sub_handler.updateCandidateRects();
        LayoutAI_App.emit_M(EventName.RoomList, this.room_list);

        LayoutContainerUtils.updateEntityUids(true);
        this.update();

        if (LayoutAI_App.instance.Configs.prepare_auto_layout) {
          this.queryModelRoomsFromServer();
          this.computeWholeLayoutSchemeList(false);
        }
      }
    }
  }

  loadEzdxfCadData(ezdxf_data: any, needs_compute_layouts: boolean = true) {
    LayoutAI_App.emit_M(EventName.WholeLayoutSchemeList, { schemeList: [], index: 0 });

    this.manager.layout_container._layout_scheme_id = null;
    this.manager.layer_CadEzdxfLayer.loadData(ezdxf_data);
    this.manager.layer_CadEzdxfLayer.makeDirty();
    this.manager.layer_CadEzdxfLayer._updateLayerContent();
    this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });

    let container = this.manager.layout_container;
    EzdxfParser.instance.clean();
    EzdxfParser.instance.initStdLayers(container.ezdxf_cad_data);
    let data = EzdxfParser.instance.parseEzdxf2SwjXmlJson(container.ezdxf_cad_data);
    if (data.wall_list.length > 0) {
      // 检测出墙
      LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(data, {
        updateUid: true,
        clean_dxf_data: false,
        layout_scheme_id: null,
        layout_scheme_name: null
      });
      let roomnames = container._room_entities.map(entity => entity.name);

      return checkRoomNamesCompleteness(roomnames) > 0.85;
    }
    return false;
  }

  loadAiCadData(data: I_AI_CadData) {
    let cad_data = new AI_CadData(
      data,
      this.manager.layout_container.ezdxf_cad_data?.view_center || { x: 0, y: 0, z: 0 }
    );

    LayoutAiCadDataParser.Container = this.manager.layout_container;
    LayoutAiCadDataParser.fromAiCadData(cad_data);
    this.manager.layout_container.focusCenter();
    this.manager.onLayerVisibilityChanged();
    this._cad_default_sub_handler.updateCandidateRects();
    this.queryModelRoomsFromServer();

    this.update();
  }

  /**
   * 初始化控件, 在react下, 需要研究怎么跟组件化配合
   *
   */
  initWidget(): void {
    return; //
  }

  enter(state?: number): void {
    super.enter(state);
    const route = window.location.pathname;
    this.manager.layout_container.drawing_figure_mode = route.includes('new3d')
      ? DrawingFigureMode.Outline
      : DrawingFigureMode.Figure2D; // 默认绘制线框图
    this.manager.layer_CadFloorLayer.visible = true;
    this.manager.layer_CadRoomFrameLayer.visible = true;
    this.manager.layer_CadCabinetLayer.visible = true;
    this.manager.layer_CadFurnitureLayer.visible = true;
    this.manager.layer_CadCabinetLayer.visible = true;
    this.manager.layer_OutLineLayer.visible = true;
    this.manager.layer_CadRoomNameLayer.visible = true;
    this.manager.layer_CadEzdxfLayer.visible = false;
    this.manager.layer_LightingLayer.visible = false;
    this.manager.layer_CeilingLayer.visible = false;
    this.manager.layer_CadCopyImageLayer.visible = false;
    this.manager.layer_CadSubAreaLayer.visible = LayoutAI_App.IsDebug ? true : false; // 分区默认在开发测试模式不显示先
    // this.manager.layer_DimensionOutterWallLayer.visible = false


    if (this.manager.drawing_layers[CadDrawingLayerType.CadDecorates]) {
      this.manager.drawing_layers[CadDrawingLayerType.CadDecorates].visible = false;
    }
    this.manager.onLayerVisibilityChanged();
    this.runCommand(this._default_sub_handler_name);
    if (this._cad_default_sub_handler) {
      this._cad_default_sub_handler.updateCandidateRects();
    }

    if (
      this.manager._previous_mode === AI2DesignBasicModes.HouseDesignMode ||
      this.manager._previous_mode === AI2DesignBasicModes.DesignMode ||
      this.manager._previous_mode === AI2DesignBasicModes.RemodelingMode ||
      this.manager._previous_mode === AI2DesignBasicModes.HouseCorrectionMode ||
      this.manager._previous_mode === AI2DesignBasicModes.EzDxfEditMode
    ) {
      this.computeWholeLayoutSchemeList(false);
    }
    if (this.manager._previous_mode === AI2DesignBasicModes.MeasurScaleMode) {
      this.computeWholeLayoutSchemeList(false).then(() => {
        if (this.whole_layout_scheme_list && this.whole_layout_scheme_list.length > 0) {
          LayoutAI_App.DispatchEvent(LayoutAI_Events.ClickWholeLayoutScheme, {
            value: this.whole_layout_scheme_list[0],
            index: 0
          });
        }
      });
    }

    this.manager.layout_container.focusCenter();

    this.manager.layout_container._furniture_entities.forEach(entity => {
      if (entity instanceof TBaseGroupEntity) {
        entity.setMatchedVisible(false);
      }
    });

    if (this.manager?.scene3DManager) {
      this.manager.scene3DManager.bindOnSelectFigure(this._onSelectedFigure.bind(this));
      this.manager.scene3DManager.bindOnSelectRoom((room: TRoom) => {
        if (room && room._room_entity) {
          let center = room._room_entity._main_rect.rect_center;
          let ev: I_MouseEvent = { posX: center.x, posY: center.y, _ev: null };
          this._cad_default_sub_handler.updateRoomSelected(ev);
          this.EventSystem.emit_M(EventName.SelectingTarget, room._room_entity, null, null);
          this._cad_default_sub_handler.onRoomEntitySelected(room._room_entity);
          this._cad_default_sub_handler.selected_target.selected_rect = room._room_entity.rect;
          this._cad_default_sub_handler.selected_target.selected_entity = room._room_entity;
          LayoutAI_App.emit(EventName.selectRoom, room._room_entity);
        } else {
          if (this.manager.layout_container._drawing_layer_mode != 'SingleRoom') {
            this.manager.layout_container._selected_room = null;
          }
          LayoutAI_App.emit(EventName.selectRoom, null);
          this._cad_default_sub_handler.cleanSelection();
          this.EventSystem.emit_M(EventName.SelectingTarget, null, null, null);
        }
      });
    }
  }

  private _onSelectedFigure(ele: TFigureElement) {
    let _figure_element_selected = ele;
    LayoutAI_App.emit_M(EventName.FigureElementSelected, _figure_element_selected);
    if (_figure_element_selected && _figure_element_selected.furnitureEntity) {
      this.EventSystem.emit_M(
        EventName.SelectingTarget,
        _figure_element_selected.furnitureEntity,
        null,
        null
      );
      this._cad_default_sub_handler.selected_target.selected_entity =
        _figure_element_selected.furnitureEntity;
      if (this.manager.layout_container.drawing_figure_mode !== DrawingFigureMode.Figure2D) {
        this._cad_default_sub_handler.selected_target.selected_rect =
          _figure_element_selected.furnitureEntity.figure_element.matched_rect;
      } else {
        this._cad_default_sub_handler.selected_target.selected_rect =
          _figure_element_selected.furnitureEntity.rect;
      }
    } else if (compareNames([ele?.sub_category], ['地面', '墙面'])) {
    } else {
      this.EventSystem.emit_M(EventName.SelectingTarget, null, null, null);
      this._cad_default_sub_handler.cleanSelection();
    }
    let scene3D = LayoutAI_App.instance.scene3D;
    if (!scene3D) return;
    if (ele) {
      scene3D.setSelectionBox(ele);
    } else {
      LayoutAI_App.emit_M(EventName.FigureElementSelected, null);
      scene3D.clearSelectionBox();
    }
  }

  leave(state: number = 0) {
    super.leave(state);

    // 离开的时候, 同步房间数据
    this.transformGroup();
    this.manager.layout_container.updateRoomsFromEntities();
    this.manager._save_to_local_model_rooms();

    this.manager.layer_CadEzdxfLayer.visible = false;

    if (this.manager.layout_container) {
      this._initial_scheme_data = this.manager.layout_container.toXmlSchemeData();
    }
    this._cad_default_sub_handler.cleanSelection();
    this.manager.layout_container.cleanDimension();
    LayoutAI_App.emit_M(EventName.FigureElementSelected, null);
  }

  onmousedown(ev: I_MouseEvent): void {
    if (ev.ctrlKey && !this._active_sub_handler) {
      this.runCommand(LayoutAI_Commands.Transform_Combination);
    }
    if (this._active_sub_handler) {
      this._active_sub_handler.onmousedown(ev);
    } else if (this._cad_default_sub_handler) {
      // 默认方法mouse_down后, 有可能触发进入新的active_handler
      this._cad_default_sub_handler.onmousedown(ev);

      if (this._active_sub_handler) {
        this._active_sub_handler.onmousedown(ev);
        return;
      }
    }
    this._last_ev_pos = { x: ev._ev.pageX, y: ev._ev.pageY };

    this._is_painter_center_moving = false;
  }

  ondbclick(ev: I_MouseEvent): void {
    if (this._active_sub_handler) {
      this._active_sub_handler.ondbclick(ev);
    } else if (this._cad_default_sub_handler) {
      this._cad_default_sub_handler.ondbclick(ev);
    }
  }

  onmousemove(ev: I_MouseEvent): void {
    if (this._active_sub_handler) {
      super.onmousemove(ev);
    } else {
      if (this._cad_default_sub_handler) {
        this._cad_default_sub_handler.onmousemove(ev);
      }
      super.onmousemove(ev);
    }
  }
  onmouseup(ev: I_MouseEvent): void {
    if (this._active_sub_handler) {
      super.onmouseup(ev);
    } else {
      if (this._cad_default_sub_handler) {
        this._cad_default_sub_handler.onmouseup(ev);
      }
      super.onmouseup(ev);
    }
  }

  onwheel(ev: WheelEvent): void {
    if (this._active_sub_handler) {
      super.onwheel(ev);
      this._active_sub_handler.onwheel(ev);
    } else {
      if (this._cad_default_sub_handler) {
        this._cad_default_sub_handler.onwheel(ev);
      }
      super.onwheel(ev);
    }
  }

  drawCanvas(): void {
    if (this.manager.layer_DefaultBatchLayer) {
      let batchDirty = !this._is_painter_center_moving && !this._is_moving_element;
      this.manager.layer_DefaultBatchLayer.drawLayer(batchDirty);
    }

    if (this.manager.layer_ExtDrawingBatchLayer) {
      if (this.manager.layer_ExtDrawingBatchLayer.visible) {
        this.manager.layer_ExtDrawingBatchLayer.drawLayer(false);
      }
    }

    if (this._active_sub_handler) {
      this._active_sub_handler.drawCanvas();
    } else if (this._cad_default_sub_handler) {
      this._cad_default_sub_handler.drawCanvas();
    }
  }

  async parseCadUrl(cadFileurl: string): Promise<void> {
    let scope = this;
    this.EventSystem.emit(EventName.OnPreparingHandle, { opening: true, title: 'CAD文件识别中' });

    console.log(cadFileurl);
    let res = await fetch(`${gatewayUrl}/oda-dxfjson/api/oda/dwgjson`, {
      method: 'POST',
      credentials: 'include',
      mode: 'cors', // no-cors, *cors, same-origin
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        dwgFile: cadFileurl
      })
    })
      .then(val => val.json())
      .catch(err => {
        console.log(err);
        this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });
      });
    if (res) {
      let json_data = decodeURIComponent(res.data.replace(/\\\\U\+/gi, '%u'));

      let data = JSON.parse(json_data);

      if (scope.loadEzdxfCadData(data)) {
        this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });

        await this.queryModelRoomsFromServer();
        await this.computeWholeLayoutSchemeList(false);

        return;
      }
    }
    this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });
    this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });
    this.EventSystem.emit(EventName.OpenCADDefaultHandle, {
      opening: true,
      content: 'CAD识别可能存在缺漏, 请注意检查；为提高识别率，建议上传标准CAD文件; 手动编辑图层。'
    });
    if (this.manager.layout_container.ezdxf_cad_data) {
      LayoutAI_App.RunCommand(AI2DesignBasicModes.EzDxfEditMode);
      LayoutAI_App.emit(EventName.EzdxfFileLoaded, true);
    }
  }

  async openCadFileDialog(): Promise<void> {
    let fileResult: FileOpenResult = await openFileInput('.dwg', 'Base64');
    const layoutSchemeName: string = fileResult.file;
    this.EventSystem.emit(EventName.LayoutSchemeOpened, { id: null, name: layoutSchemeName });
    this.manager.layout_container._layout_scheme_name = layoutSchemeName;
    let base64_str: string = fileResult.content;
    this.EventSystem.emit(EventName.OnPreparingHandle, { opening: true });
    let id = base64_str.indexOf('base64,');

    base64_str = base64_str.substring(id + 'base64,'.length);
    let res = await fetch(`${gatewayUrl}/oda-dxfjson/api/oda/dwg64json`, {
      method: 'POST',
      credentials: 'include',
      mode: 'cors', // no-cors, *cors, same-origin
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        dwgFile: base64_str
      })
    })
      .then(val => val.json())
      .catch(err => {
        console.log(err);
        this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });
      });

    if (res) {
      let json_data = decodeURIComponent(res.data.replace(/\\\\U\+/gi, '%u'));

      let data = JSON.parse(json_data);

      if (this.loadEzdxfCadData(data)) {
        this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });
        await this.queryModelRoomsFromServer();
        await this.computeWholeLayoutSchemeList(false);
        return;
      }
    }

    this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });
    this.EventSystem.emit(EventName.OpenCADDefaultHandle, {
      opening: true,
      content: 'CAD识别可能存在缺漏, 请注意检查；为提高识别率，建议上传标准CAD文件; 手动编辑图层。'
    });
    if (this.manager.layout_container.ezdxf_cad_data) {
      this._manager.layer_CadCopyImageLayer.clean();
      LayoutAI_App.RunCommand(AI2DesignBasicModes.EzDxfEditMode);
      LayoutAI_App.emit(EventName.EzdxfFileLoaded, true);
    }
  }

  // 从个人工作台跳转过来的
  async openCadFile(): Promise<any> {
    const file = await loadFile('DwgBase64');
    const fileData = (file as { data: any }).data;
    const layoutSchemeName: string = '';
    this.EventSystem.emit(EventName.LayoutSchemeOpened, { id: null, name: layoutSchemeName });
    this.manager.layout_container._layout_scheme_name = layoutSchemeName;
    let base64_str: string = fileData;
    this.EventSystem.emit(EventName.OnPreparingHandle, { opening: true });
    let id = base64_str.indexOf('base64,');

    base64_str = base64_str.substring(id + 'base64,'.length);
    let res = await fetch(`${gatewayUrl}/oda-dxfjson/api/oda/dwg64json`, {
      method: 'POST',
      credentials: 'include',
      mode: 'cors', // no-cors, *cors, same-origin
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        dwgFile: base64_str
      })
    })
      .then(val => val.json())
      .catch(err => {
        console.log(err);
        this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });
        return null;
      });

    if (res) {
      let json_data = decodeURIComponent(res.data.replace(/\\\\U\+/gi, '%u'));

      let data = JSON.parse(json_data);

      if (this.loadEzdxfCadData(data)) {
        this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });
        await this.queryModelRoomsFromServer();
        await this.computeWholeLayoutSchemeList(false);
        LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);
        return null;
      }
    }

    this.EventSystem.emit(EventName.OnPreparingHandle, { opening: false });
    this.EventSystem.emit(EventName.OpenCADDefaultHandle, {
      opening: true,
      content: 'CAD识别可能存在缺漏, 请注意检查；为提高识别率，建议上传标准CAD文件; 手动编辑图层。'
    });
    if (this.manager.layout_container.ezdxf_cad_data) {
      this._manager.layer_CadCopyImageLayer.clean();
      LayoutAI_App.RunCommand(AI2DesignBasicModes.EzDxfEditMode);
      LayoutAI_App.emit(EventName.EzdxfFileLoaded, true);
    }

    return null;
  }

  async openImitateImage() {
    let fileResult: FileOpenResult = await openFileInput('image/*').catch(e => {
      return null;
    });

    if (!fileResult) return;
    this.loadImitateImageFile(fileResult.content);
  }
  // 识别临摹图
  async loadImitateImageFile(base64_str: string) {
    LayoutAI_App.emit(EventName.OpenHouseSearching, false);

    // 创建一个Image对象
    let img = new Image();
    img.src = base64_str;

    img.onload = () => {
      let canvas = document.createElement('canvas');
      let ctx = canvas.getContext('2d');
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      // 获取转换后的图像数据（JPG格式，RGB）
      let jpgBase64 = canvas.toDataURL('image/jpeg');
      if (checkIsMobile()) {
        this._postHouseRecognitionRequest(jpgBase64);
      } else {
        LayoutAI_App.emit(EventName.ShowRoomImagePredictDlg, jpgBase64);
      }
    };
  }
  // 直接调用户型识别接口
  private _postHouseRecognitionRequest(jpgBase64: string) {
    // 识别户型图开始
    LayoutAI_App.emit(EventName.OpeningImitateFileStart, null);
    trialDataService
      .requestHouseRecognition(jpgBase64)
      .then(async (res: any) => {
        if (res.Status != 200 || !res.Content) {
          this._handleException();
          return;
        }
        const houseXmlContent = res.Content;
        const houseXmlBase64 = deflate_to_base64_str(houseXmlContent);
        let xmlSchemeJson: I_SwjXmlScheme = await SchemeXmlParseService.parseSchemeXml2Json(
          houseXmlBase64,
          GenDateUUid()
        );
        if (!xmlSchemeJson) {
          this._handleException();
          return;
        }

        LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(xmlSchemeJson);

        this.update();
        LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false });
        let config = {
          duration: 2,
          content: t('识别成功').toString(),
          className: 'custom-class',
          style: {
            marginTop: '4vh'
          }
        };
        message.success(config);
        this.computeWholeLayoutSchemeList(false);
      })
      .catch((e: any) => {
        this._handleException();
      });
  }

  //异常处理
  private _handleException(): void {
    message.error('识别失败，请重新上传户型图');
    LayoutAI_App.RunCommand(AI2DesignBasicModes.AiCadMode);
    LayoutAI_App.emit(EventName.OpeningImitateFileFail, { opening: false });
  }

  changeSpaceName(room: TRoomEntity, value: string) {
    if (room) {
      room.name = value;
      this.update();
    }
  }

  async loadRoomTemplate(id: string, room_id: string) {
    TRoomTemplateSaver.loadRoomTemplate(id, room_id);
    return false;
  }

  addWinDoorEntity(
    type: IRoomEntityType,
    realType: IRoomEntityRealType,
    label: string,
    furnitureTitle: string
  ) {
    let addedFurniture = g_FigureImagePaths[furnitureTitle];
    this._selected_target.selected_rect = null;
    let I_SwjDoor = TBaseEntity.makeSimpleEntityData(type, realType);
    let entity = new TWindowDoorEntity(I_SwjDoor, null);
    entity.length = 900;
    entity.width = 240;
    entity._rect._w = addedFurniture.length;
    entity._rect._h = addedFurniture.depth;
    entity._rect.rect_center = new Vector3(-10000, -10000, 0);
    this.ai_cad_data._adding_figure_entity = entity;
    this._selected_target.selected_rect = entity._rect;
    this._selected_target.selected_rect.ex_prop['label'] = label;
    TBaseEntity.set_polygon_type(this._selected_target.selected_rect, type);
    this._selected_target.selected_transform_element = new T_MoveWinDoorElement(
      this._selected_target,
      this.manager.layout_container
    );
    let _previous_rect = this._selected_target.selected_rect.clone();
    this._selected_target.selected_transform_element.recordOriginRect(_previous_rect);
    this._selected_target.selected_transform_element.bindTargetRect(entity._rect, []);
    LayoutAI_App.RunCommand(LayoutAI_Commands.Transform_MovingStruture);
    this._is_moving_element = true;
  }

  addGroupTemplate(furnitureTitle: string, type: boolean = false) {
    const group_template_prefix = 'GroupTemplate:';
    let group_code = furnitureTitle.substring(group_template_prefix.length);

    let group_template = new TGroupTemplate();
    group_template.group_code = group_code;

    let info = TGroupTemplate.GroupCodeUiInfo[group_code];

    group_template._target_rect = new ZRect(
      info?.default_length || 100000,
      info?.default_depth || 100000
    );
    group_template.updateByTargetRect();

    if (group_template.current_s_group) {
      let rect = new ZRect(1, 1);
      rect.ex_prop['label'] = furnitureTitle;
      rect.ex_prop['is_edit'] = 'is_edit';
      rect._nor.set(0, -1, 0);
      rect._w = group_template.current_s_group.group_rect.w;
      rect._h = group_template.current_s_group.group_rect.h;
      // rect.rect_center = new Vector3(-9999999, -9999999, 0);
      // if (!type) {
      //   rect.rect_center = this.painter.p_center;
      // } else {
      //   rect.rect_center = new Vector3(-9999999, -9999999, 0);
      // }
      rect.updateRect();
      group_template._target_rect = rect;
      group_template.updateByTargetRect();
      rect._attached_elements[TGroupTemplate.EntityName] = group_template;
      TBaseEntity.set_polygon_type(rect, AI_PolyTargetType.Furniture);
      let entity = TGroupTemplateEntity.getOrMakeEntityOfCadRect(rect) as TGroupTemplateEntity;
      let group_entity = entity.toBaseGroupEntity() as TBaseGroupEntity;
      group_entity.updateSize();

      if(group_entity._combination_entitys.length > 0)
      {
        for(let entity of group_entity._combination_entitys)
        {
          T_TransformElement.recordOriginRectData(entity.rect);
        }
      }
      T_TransformElement.recordOriginRectData(group_entity._rect);
      return group_entity;
    }
  }

  addStructureEntity(
    type: IRoomEntityType,
    realType: IRoomEntityRealType,
    label: string,
    furnitureTitle: string
  ) {
    let addedFurniture = g_FigureImagePaths[furnitureTitle];
    let I_SwjStructure = TBaseEntity.makeSimpleEntityData(type, realType);
    let entity = new TStructureEntity(I_SwjStructure, null);
    entity.length = 900;
    entity.width = 240;
    entity._rect._w = addedFurniture.length;
    entity._rect._h = addedFurniture.depth;
    entity._rect.ex_prop['label'] = realType;
    entity._rect._nor.set(0, -1, 0);
    if (!type) {
      entity.rect.rect_center = this.painter.p_center;
    } else {
      entity._rect.rect_center = new Vector3(-9999999, -9999999, 0);
    }
    this.ai_cad_data._adding_figure_entity = entity;
    TBaseEntity.set_polygon_type(entity._rect, type);
    this._is_moving_element = true;
    LayoutAI_App.RunCommand(LayoutAI_Commands.AddFurniture); //结构件也是调用图元的添加handler
  }

  onFigureSelected(furnitureTitle: string, needs_add_entity: boolean) {
    const WinDoorMapping: {
      [key: string]: { type: IRoomEntityType; name: IRoomEntityRealType; alias: string };
    } = {
      单开门: { type: 'Door', name: 'SingleDoor', alias: 'door' },
      推拉门: { type: 'Door', name: 'SlidingDoor', alias: 'slidedoor' },
      一字窗: { type: 'Window', name: 'OneWindow', alias: 'window' },
      飘窗: { type: 'Window', name: 'BayWindow', alias: 'baywindow' },
      双开门: { type: 'Door', name: 'DoubleDoor', alias: 'doubledoor' },
      子母门: { type: 'Door', name: 'SafetyDoor', alias: 'safetydoor' },
      门洞: { type: 'Door', name: 'DoorHole', alias: 'doorhole' },
      垭口: { type: 'Door', name: 'PassDoor', alias: 'passdoor' },
      栏杆: { type: 'Window', name: 'Railing', alias: 'railing' }
    };
    const StructureMapping: {
      [key: string]: { type: IRoomEntityType; name: IRoomEntityRealType; alias: string };
    } = {
      包管: { type: 'StructureEntity', name: 'Envelope_Pipe', alias: 'Envelope_Pipe' },
      地台: { type: 'StructureEntity', name: 'Platform', alias: 'Platform' },
      方柱: { type: 'StructureEntity', name: 'Pillar', alias: 'Pillar' },
      横梁: { type: 'StructureEntity', name: 'Beam', alias: 'Beam' },
      烟道: { type: 'StructureEntity', name: 'Flue', alias: 'Flue' }
    };

    let winDoorKey = Object.keys(WinDoorMapping).find(key => furnitureTitle.includes(key));
    let matchedKey = Object.keys(StructureMapping).find(key => furnitureTitle.includes(key));
    if (furnitureTitle.indexOf('GroupTemplate:') >= 0) {
      let entity = this.addGroupTemplate(furnitureTitle, needs_add_entity);
      this.ai_cad_data._adding_figure_entity = entity;
      this._is_moving_element = true;
      if (needs_add_entity) {
        LayoutAI_App.RunCommand(LayoutAI_Commands.AddFurniture);
      }
    }

    if (winDoorKey) {
      let { type, name, alias } = WinDoorMapping[winDoorKey];
      this.addWinDoorEntity(type, name, alias, furnitureTitle);
    } else if (matchedKey) {
      let { type, name, alias } = StructureMapping[matchedKey];
      this.addStructureEntity(type, name, alias, furnitureTitle);
    } else {
      let furniture_toadd = g_FigureImagePaths[furnitureTitle];
      let rect = new ZRect(1, 1);
      if (furniture_toadd) {
        rect.reParaFromVertices(2);
        rect.ex_prop['label'] = furnitureTitle;
        rect.ex_prop['is_edit'] = 'is_edit';
        rect._nor.set(0, -1, 0);
        rect._w = furniture_toadd.length;
        rect._h = furniture_toadd.depth;
        if (!needs_add_entity) {
          rect.rect_center = this.painter.p_center;
        } else {
          rect.rect_center = new Vector3(-9999999, -9999999, 0);
        }
        TBaseEntity.set_polygon_type(rect, AI_PolyTargetType.Furniture);
        let entity = TFurnitureEntity.getOrMakeEntityOfCadRect(rect);
        this.ai_cad_data._adding_figure_entity = entity;

        this._is_moving_element = true;
        if (needs_add_entity) {
          LayoutAI_App.RunCommand(LayoutAI_Commands.AddFurniture);
        }
      }
    }
  }

  addBaseGroupEntity(event_param: string) {
    let group_rect = new ZRect(1, 1);
    let combination_rect = this._selected_target.selected_rect;
    group_rect._w = combination_rect._w;
    group_rect._h = combination_rect._h;
    group_rect._nor = combination_rect._nor.clone();
    group_rect.ex_prop['poly_target_type'] = 'BaseGroup';
    group_rect.ex_prop['GroupName'] = event_param;
    group_rect.rect_center = combination_rect.rect_center.clone();
    group_rect._attached_elements['combination_entitys'] = [
      ...this._selected_target.selected_combination_entitys
    ];
    group_rect.updateRect();
    let baseGroup_entity = TBaseGroupEntity.getOrMakeEntityOfCadRect(
      group_rect
    ) as TBaseGroupEntity;
    baseGroup_entity.combination_entitys = [...this._selected_target.selected_combination_entitys];
    baseGroup_entity.updateCategory(event_param);
    let info = new T_GroupOperationInfo(this.manager, 'Group');
    info._group_base_entity = baseGroup_entity;
    info._furniture_entities = this._selected_target.selected_combination_entitys.map(
      (entity: TFurnitureEntity) => {
        TBaseGroupEntity.recordGroupRectData(entity, baseGroup_entity);
        return entity;
      }
    );
    info.redo();

    this.manager.appendOperationInfo(info);
    this._selected_target.selected_rect = baseGroup_entity._rect;
    this._selected_target.selected_combination_entitys = baseGroup_entity.combination_entitys;
    this._selected_target.selected_entity = baseGroup_entity;
    this._selected_target.selected_group_entity = baseGroup_entity;
    this._cad_default_sub_handler.updateCandidateRects();
    this._cad_default_sub_handler.updateTransformElements();
    this._cad_default_sub_handler.updateSelectionState();
    this._cad_default_sub_handler.updateAttributes('init');
    let rightTop = this._cad_default_sub_handler.computeRightVerical();
    let _pp = this.painter.worldToCanvas(rightTop);
    this.EventSystem.emit_M(
      EventName.SelectingTarget,
      this._selected_target.selected_rect,
      null,
      _pp
    );
    this.update();
  }

  checkUnfurnishedRooms(): boolean {
    let hasUnfurishedRoom = TSeriesFurnisher.instance.current_rooms.some(
      (room: TRoom) => room.hasUnFurnishedApplyScope() || room.hasUnFurnishedFigureElement()
    );
    if (hasUnfurishedRoom) {
      setTimeout(
        () =>
          LayoutAI_App.emit(EventName.PerformFurnishResult, {
            progress: 'furnishremaining',
            message: '需要补全布置'
          }),
        10
      );
    }
    return !hasUnfurishedRoom;
  }

  async handleEvent(event_name: string, event_param: any) {
    super.handleEvent(event_name, event_param);
    if (event_name === LayoutAI_Events.SelectedFurniture) {
      const furnitureTitle = event_param;
      if (furnitureTitle) {
        // 在这里做匹配逻辑
        // 1、先创建图元，但是目前没有全部图元的初始数据，只能从单个CAD已经识别出来的图元中获取
        this._cad_default_sub_handler.cleanSelection();
        this._cad_default_sub_handler.updateSelectionState();
        this.onFigureSelected(furnitureTitle, true);
      }
    }
    if (event_name === LayoutAI_Events.SelectedMaterial) {
      const material = event_param;
      if (material) {
        let rect = new ZRect(1, 1);
        rect.ex_prop['label'] = material.modelLoc;
        rect.ex_prop['is_edit'] = 'is_edit';
        rect._nor.set(0, -1, 0);
        rect._w = material.length;
        rect._h = material.width;
        rect.rect_center = new Vector3(-9999999, -9999999, 0);
        AI_CadData.set_polygon_type(rect, AI_PolyTargetType.Furniture);
        let entity = AI_CadData.getOrMakeEntityOfCadRect(rect) as TFurnitureEntity;
        this.ai_cad_data._adding_figure_entity = entity;
        this._is_moving_element = true;
        this.ai_cad_data._adding_figure_entity._figure_element.modelLoc = material.modelLoc;
        this.ai_cad_data._adding_figure_entity._figure_element.category = material.modelLoc;
        this.ai_cad_data._adding_figure_entity._figure_element.sub_category = material.modelLoc;
        if (!entity.figure_element.matched_rect) {
          entity.figure_element.matched_rect = rect.clone();
        }

        await MaterialService.materialMatching(
          [this.ai_cad_data._adding_figure_entity.figure_element],
          Number(TSeriesFurnisher.instance._current_series.kgId),
          TSeriesFurnisher.instance._current_series.seriesName,
          '',
          '',
          '',
          0,
          this._logger.traceId
        );
        // 从套系中匹配的材质中找到对应的matched_material
        let candidate_materials =
          this.ai_cad_data._adding_figure_entity.figure_element._candidate_materials;
        if (candidate_materials?.length > 0) {
          let matched_material = candidate_materials.find(
            item => item.modelId == material.materialId
          );
          if (matched_material) {
            this.ai_cad_data._adding_figure_entity.figure_element._matched_material =
              matched_material;
          }
        }
        // 更新targetSize等属性
        this.ai_cad_data._adding_figure_entity._figure_element.updateMatchedMaterialByRect();
        MatchingPostProcesser.createTopViewTextureForSingleFigureElement([
          this.ai_cad_data._adding_figure_entity.figure_element
        ]);
        await FigureTopViewer.instance.updateFigureWireFrameImage(
          this.ai_cad_data._adding_figure_entity.figure_element
        );
        this.update();
        LayoutAI_App.RunCommand(LayoutAI_Commands.AddFurniture);
      }
    }
    if (event_name === LayoutAI_Events.SelectedSquareMaterial) {
      const material = event_param;
      if (material) {
        let info = await getDesignMaterialByIdsWithOutPlaceheights({
          materialIds: material.id
        });
        let materialInfo = info.result.result[0];
        let rect = new ZRect(1, 1);
        rect.ex_prop['label'] = materialInfo.MaterialName;
        rect.ex_prop['is_edit'] = 'is_edit';
        rect._nor.set(0, -1, 0);
        rect._w = Math.round(materialInfo.PICLength);
        rect._h = Math.round(materialInfo.PICWidth);
        rect.rect_center = new Vector3(-9999999, -9999999, 0);
        AI_CadData.set_polygon_type(rect, AI_PolyTargetType.Furniture);
        let entity = AI_CadData.getOrMakeEntityOfCadRect(rect) as TFurnitureEntity;
        this.ai_cad_data._adding_figure_entity = entity;
        this._is_moving_element = true;
        if (!entity.figure_element.matched_rect) {
          entity.figure_element.matched_rect = rect.clone();
        }
        entity.figure_element._matched_material.modelId = material.id;
        entity.figure_element._matched_material.width = materialInfo.PICWidth;
        entity.figure_element._matched_material.length = materialInfo.PICLength;
        entity.figure_element._matched_material.originalHeight = materialInfo.PICHeight;
        entity.figure_element._matched_material.imageUrl = materialInfo.ImagePath.startsWith(
          'https://'
        )
          ? materialInfo.ImagePath
          : `https://material.3vjia.com/${materialInfo.ImagePath}`;
        entity.category = materialInfo.MaterialName;
        // 更新targetSize等属性
        this.ai_cad_data._adding_figure_entity._figure_element.updateMatchedMaterialByRect();
        MatchingPostProcesser.createTopViewTextureForSingleFigureElement([entity.figure_element]);
        await FigureTopViewer.instance.updateFigureWireFrameImage(entity.figure_element);
        this.update();
        LayoutAI_App.RunCommand(LayoutAI_Commands.AddFurniture);
      }
    }
    if (event_name === LayoutAI_Events.mobileAddFurniture) {
      const furnitureTitle = event_param;
      if (furnitureTitle) {
        // 在这里做匹配逻辑
        // 1、先创建图元，但是目前没有全部图元的初始数据，只能从单个CAD已经识别出来的图元中获取
        this._cad_default_sub_handler.cleanSelection();
        this._cad_default_sub_handler.updateSelectionState();
        this.onFigureSelected(furnitureTitle, false);
        this._cad_default_sub_handler.addNewEntitiy();
      }
    }
    if (event_name === LayoutAI_Events.MoveFurniture) {
      this.ai_cad_data._adding_figure_entity = null;
      this.update();
    }
    if (event_name === LayoutAI_Events.UpdateSize) {
      // this._select_target_handler.updateSize(event_param)
      this.update();
    }
    if (event_name === LayoutAI_Events.OpenIssueScheme) {
      let swj_scheme_json: string = event_param.schemeJson;

      if (swj_scheme_json) {
        const schemeJson = JSON.parse(event_param.schemeJson.replace(/'/g, '"'));

        if (schemeJson.scheme_id === this.manager.layout_container._scheme_id) {
          console.log('异常的方案正在处理中...');
          return;
        }

        LayoutSchemeXmlJsonParser.loadSwjSchemeXmlJson(schemeJson as I_SwjXmlScheme);
      }
    }
    if (event_name === LayoutAI_Events.HandleUnGroupTemplate) {
      let entity = TBaseEntity.getEntityOfRect(this._selected_target.selected_rect);
      if (this._cad_default_sub_handler && entity != null) {
        if (entity.type === 'BaseGroup') {
          this._cad_default_sub_handler.unBaseGroup((entity as TBaseGroupEntity) || null);
        }
        if (entity.type === 'Furniture') {
          this._cad_default_sub_handler.unGroupTemplate((entity as TGroupTemplateEntity) || null);
        }
      }
      this.EventSystem.emit_M(EventName.SelectingTarget, null);
    }

    if (event_name === LayoutAI_Events.CadAutoDecorations) {
      this.cad_auto_decorations();
    }
    if (event_name === LayoutAI_Events.ChangeSpaceName) {
      this.changeSpaceName(event_param.room, event_param.value);
    }

    if (event_name === LayoutAI_Events.SingleRoomLayout) {
      this._selectSingleRoom(event_param);
      this.update();
    }
    if (event_name === LayoutAI_Events.leaveSingleRoomLayout) {
      LayoutAI_App.emit(EventName.ShowSubHandlerBtn, false);
      this.manager.layout_container._drawing_layer_mode = 'FullHouse';
      this.manager.layout_container._room_entities.forEach(
        (item: TRoomEntity) => (item.isSingle = false)
      );
      this.painter.importTransformData(this._painter_ts, false);
      this.EventSystem.emit_M(EventName.SelectingTarget, null, null, null);
      this.update();
    }
    if (event_name === LayoutAI_Events.AddSubRoomArea) {
      if (!event_param?._room) return;
      if (this.manager.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing]) {
        this.manager.drawing_layers[CadDrawingLayerType.CadSubRoomAreaDrawing].visible = true;
      }
      this.manager.onLayerVisibilityChanged();
      this._selectSingleRoom(event_param);
      let room = event_param._room;
      roomSubAreaService.createSubAreaByRoom(room);
      this.update();
    }

    if (event_name === LayoutAI_Events.CreateCombination) {
      if (!event_param) return;
      this.addBaseGroupEntity(event_param);
    }

    if (event_name === LayoutAI_Events.ReplaceEntity) {
      this.replaceEntity(event_param);
    }

    if (event_name === LayoutAI_Events.setFocus) {
      if (!checkIsMobile()) {
        const params = FocusMap[event_param.focus];
        this.painter._p_sc = params.focus;
      } else {
        const params = MobileFocusMap[event_param.focus];
        this.painter._p_sc = params.focus;
      }
      if (this.manager.layout_container._drawing_layer_mode === 'SingleRoom') {
        let center = this.main_rect.rect_center;
        this.painter.p_center = center;
        this.update();
        return;
      }
      this.manager.layout_container.focusCenter();

      this.update();
    }

    if (event_name === LayoutAI_Events.SeriesSampleSelected) {
      // this.manager.layout_container.updateRoomsFromEntities(false,false);
      TSeriesFurnisher.instance.updateCurrentRooms();
      TSeriesFurnisher.instance.onSeriesSampleSelected(event_param.scope, event_param.series);
    }
    if (event_name == LayoutAI_Events.Trim) {
      TLayoutFineTuningManagerToolUtil.instance.fineTuningRoom(event_param);
    }
    if (event_name == LayoutAI_Events.ShowLivingRoomSpace) {
      event_param._room_entity.isShowSpace = !event_param._room_entity.isShowSpace;

      this.update();
    }
    if (event_name === LayoutAI_Events.ClearSeries) {
      TSeriesFurnisher.instance.clearRoom2SeriesSample(event_param, true);
      TSeriesFurnisher.instance.emitSeriesSamplesWithOrdering();
      this.manager.updateScene3D();
      this.update();
    }
    if (event_name === LayoutAI_Events.autoSave) {
      await LayoutSchemeService.autoSave(this.manager.layout_container);
      LayoutAI_App.emit_M(EventName.RoomList, this.room_list);
    }

    if (event_name === LayoutAI_Events.ReplaceMaterial) {
      let material_item = event_param as I_MaterialMatchingItem;
      let figure_element = material_item.figureElement;
      if (!figure_element) {
        figure_element = (this._selected_target.selected_entity as TFurnitureEntity)
          ?.figure_element;
      }
      LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: true, title: '替换模型...' });

      await TMaterialMatcher.instance.replaceMaterial(
        figure_element,
        event_param as I_MaterialMatchingItem,
        this.room_list
      );
      this._onSelectedFigure(event_param?.figureElement);
      LayoutAI_App.emit(EventName.ApplySeriesSample, { seriesOpening: false, title: '' });
    }
    if (event_name === LayoutAI_Events.updateLast_pos) {
      this._last_ev_pos = null;
    }
    if (event_name === LayoutAI_Events.Match3dPreviewMaterials) {
      TSeriesFurnisher.instance.tryToFurnishRoomsFor3dPreview(this.room_list);
    }

    if (event_name === LayoutAI_Events.LoadImitateImageFile) {
      this.loadImitateImageFile(event_param);
    }
    if (event_name === LayoutAI_Events.CheckUnfurnishedBeforePerformFurnish) {
      if (event_param.skipCheckUnfurnished || this.checkUnfurnishedRooms()) {
        await this.checkAndUpdateSchemeXml();
        await this.manager.layout_container.saveSchemeLayout2Json();
        LayoutAI_App.emit(EventName.CheckUnfurnishedResult, {
          layoutSchemeId: this.manager.layout_container._layout_scheme_id
        });
      }
    }
    if (event_name === LayoutAI_Events.ApplyFurnishTo3D) {
      SensorsLogger.trackApplyTo3DEvent('mySchemeToCurrent3d');
      TSeriesFurnisher.instance.applyFurnishTo3D();
    }
    if (event_name === LayoutAI_Events.PrepareRestartFurnishRemaining) {
      this.room_list.forEach(room => room.prepareRestartFurnishRemaining());
    }
    if (event_name === LayoutAI_Events.StartAutoFurnishRemaining) {
      if (ENV != 'prod') console.info('[Auto Furnish Remaining] Start');
      let pageIndex = 1;
      let matchingCurrentRoomList: TRoom[] = TSeriesFurnisher.instance.current_rooms;
      let unFinishedRoomList: TRoom[] = matchingCurrentRoomList.filter(
        room => room.hasUnFurnishedApplyScope() || room.hasUnFurnishedFigureElement()
      );
      while (unFinishedRoomList.length > 0) {
        const organizationSeries = await getKgSchemeListOfPlatform(pageIndex, 10);
        if (!organizationSeries || organizationSeries.length == 0) {
          break;
        }
        if (ENV != 'prod')
          console.info(
            '[Auto Furnish Remaining] pageIndex=' +
              pageIndex +
              ',organizationSeries.length=' +
              organizationSeries.length
          );
        for (let series of organizationSeries) {
          if (ENV != 'prod') {
            let logContent =
              '[Auto Furnish Remaining] UnFinished RoomList:\n  ' +
              unFinishedRoomList.map(room => room.toString()).join('\n  ');
            logContent += '\nRemaining use Series: ' + series.ruleName + ' ' + series.id;
            console.info(logContent);
          }
          TSeriesFurnisher.instance.current_rooms = unFinishedRoomList;
          await TSeriesFurnisher.instance.onSeriesSampleSelected(
            { soft: false, cabinet: false, hard: false, remaining: true },
            series
          );
          unFinishedRoomList = matchingCurrentRoomList.filter(
            room => room.hasUnFurnishedApplyScope() || room.hasUnFurnishedFigureElement()
          );
          if (unFinishedRoomList.length == 0) {
            break;
          }
          break;
        }
        pageIndex++;
        break;
      }

      pageIndex = 1;
      while (unFinishedRoomList.length > 0) {
        const platformSeries = await getKgSchemeListOfPlatform(pageIndex, 10);
        if (!platformSeries || platformSeries.length == 0) {
          break;
        }
        if (ENV != 'prod')
          console.info(
            '[Auto Furnish Remaining] pageIndex=' +
              pageIndex +
              ', platformSeries.length=' +
              platformSeries.length
          );
        for (let series of platformSeries) {
          if (ENV != 'prod') {
            let logContent =
              '[Auto Furnish Remaining] UnFinished RoomList:\n  ' +
              unFinishedRoomList.map(room => room.toString()).join('\n  ');
            logContent += '\n  Remaining use Series: ' + series.ruleName + ' ' + series.id;
            console.info(logContent);
          }
          TSeriesFurnisher.instance.current_rooms = unFinishedRoomList;
          await TSeriesFurnisher.instance.onSeriesSampleSelected(
            { soft: false, cabinet: false, hard: false, remaining: true },
            series
          );
          unFinishedRoomList = matchingCurrentRoomList.filter(
            room => room.hasUnFurnishedApplyScope() || room.hasUnFurnishedFigureElement()
          );
          if (unFinishedRoomList.length == 0) {
            break;
          }
          break;
        }
        pageIndex++;
        break;
      }

      TSeriesFurnisher.instance.current_rooms = matchingCurrentRoomList;
      if (ENV != 'prod') console.info('[Auto Furnish Remaining] Finished');
      LayoutAI_App.emit(EventName.FurnishRemainingFinished, null);
    }
  }

  private _selectSingleRoom(event_param: any) {
    if (this.manager.layout_container._drawing_layer_mode === 'SingleRoom') {
      // 切换单空间
      this.manager.layout_container._selected_room = event_param.makeTRoom(
        this.manager.layout_container._furniture_entities,
        false
      );
      this._cad_default_sub_handler.selected_target.selected_entity = event_param;
      this._cad_default_sub_handler.selected_target.selected_rect = event_param._rect;
      // 切换空间关闭小白条
      this.EventSystem.emit_M(EventName.SelectingTarget, null, null, null);
      LayoutAI_App.emit(EventName.selectRoom, event_param);
    } else {
      this._painter_ts = this.painter.exportTransformData();
    }
    this.manager.layout_container._room_entities.forEach(
      (item: TRoomEntity) => (item.isSingle = false)
    );
    event_param.isSingle = true;
    this.manager.layout_container._drawing_layer_mode = 'SingleRoom';
    this._cad_default_sub_handler.updateCandidateRects();
    LayoutAI_App.emit(EventName.ShowSubHandlerBtn, true);
    this.main_rect = ZRect.computeMainRect(event_param._room.room_shape._poly);
    let center = this.main_rect.rect_center;

    let ww = Math.max(this.main_rect.w, this.main_rect.h);

    let canvasElement = this.painter._canvas;
    let scaleW = canvasElement.width / ww;
    let scaleH = canvasElement.height / ww;
    this._cad_default_sub_handler.onRoomEntitySelected(event_param);
    if (checkIsMobile()) {
      let _center = this.main_rect.rect_center.clone().setY(this.main_rect.rect_center.y + 500);
      this.painter.p_center = _center;
      this.painter._p_sc = (scaleW > scaleH ? scaleH : scaleW) * 0.4;
    } else {
      this.painter.p_center = center;
      this.painter._p_sc = (scaleW > scaleH ? scaleH : scaleW) * 0.7;
    }
  }

  replaceEntity(item: any) {
    let entity = this._selected_target.selected_rect._attached_elements.Entity as TFurnitureEntity;
    let origin_nor = entity.rect?.nor.clone();
    let origin_center = entity.rect?.rect_center.clone();
    if (!entity) {
      return;
    }

    if (entity.is_single_furniture) {
      let info = new T_ReplaceFurnitureLabelOperationInfo(this.manager);
      info.setEntity(entity, item.title);
      info.redo();
      this.manager.appendOperationInfo(info);
      this._cad_default_sub_handler.updateCandidateRects();
      this._cad_default_sub_handler.updateAttributes('edit');

      this.update();
    } else {
      let label = 'GroupTemplate:' + item.group_code;
      let group_template_entity = this.addGroupTemplate(label);
      group_template_entity.rect.copy(entity.rect);
      group_template_entity._need_update = true;
      group_template_entity.update();

      let new_entity = group_template_entity.toBaseGroupEntity() as TBaseGroupEntity;

      new_entity.rect.nor = origin_nor;
      new_entity.rect.rect_center = origin_center;
      new_entity.rect.updateRect();
      new_entity.rotateAllEntityMembersAlignToGroup();
      let info = new T_ReplaceGroupOperationInfo(this.manager);
      info._entity = entity as TBaseGroupEntity;
      info.new_entity = new_entity;
      info.redo(this.manager);
      this.manager.appendOperationInfo(info);
      this._cad_default_sub_handler.updateCandidateRects();
      this._selected_target.selected_rect = new_entity._rect;
      this._cad_default_sub_handler.updateAttributes('init');
      this.update();
    }
  }

  cad_auto_decorations() {
    this.manager.layout_container.updateRoomsFromEntities(false);
  }

  async runCommand(cmd_name: string): Promise<void> {
    if (!cmd_name) return;

    super.runCommand(cmd_name);
    if (cmd_name === LayoutAI_Commands.OpenDwgFile) {
      this._cad_data_changed = true;
      this.openCadFileDialog();
    }
    if (cmd_name === LayoutAI_Commands.OpenDwgFilefromWork) {
      this._cad_data_changed = true;
      this.openCadFile();
    }
    if (cmd_name === LayoutAI_Commands.OpenImitateImage) {
      this.openImitateImage();
    }
    // 删除
    else if (cmd_name === LayoutAI_Commands.DeleteFurniture) {
      this._cad_default_sub_handler.deleteElement();
    } else if (cmd_name === LayoutAI_Commands.DeleteRuler) {
      this.manager.layout_container._ruler_entities.forEach((entity, index) => {
        if (entity.is_selected) {
          this.manager.layout_container._ruler_entities.splice(index, 1);
        }
      });
      this.update();
    }
    // 旋转
    else if (cmd_name === LayoutAI_Commands.RotateFurniture) {
      this._cad_default_sub_handler.rotate();
    }
    // 镜像
    else if (cmd_name === LayoutAI_Commands.FlipFurniture) {
      this._cad_default_sub_handler.flip();
    }
    // 镜像
    else if (cmd_name === LayoutAI_Commands.FlipFurnitureVertical) {
      this._cad_default_sub_handler.flipVertical();
    }
    // 复制
    else if (cmd_name === LayoutAI_Commands.CopyFurniture) {
      this._cad_default_sub_handler.copySelectedTarget();
    } else if (cmd_name === LayoutAI_Commands.ToAiCadMode) {
      this._cad_default_sub_handler.toAiCadMode();
    } else if (cmd_name === LayoutAI_Commands.Empty) {
      this._cad_default_sub_handler.cleanData();
    } else if (cmd_name === LayoutAI_Commands.Reset) {
      this.reset();
    } else if (cmd_name === LayoutAI_Commands.PostAddDownlights) {
      let rooms: TRoom[] = [];
      if (this.manager.layout_container._selected_room) {
        rooms = [this.manager.layout_container._selected_room];
      } else {
        rooms = this.room_list;
      }

      rooms.forEach(room => {
        TPostDecoratesLayout.post_add_lighting(room, room._furniture_list, {
          add_decoration: true,
          add_main_lights: false
        });
      });
      this.update();
    } else if (cmd_name === LayoutAI_Commands.QuerySpaceTemplates) {
      let solver = this.manager.layout_graph_solver;
      let promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(
          solver.queryModelSubSpacesFromServer(null, { pageIndex: i + 1, source: 'SubSpace' })
        );
      }
      Promise.allSettled(promises);
    } else if (cmd_name === LayoutAI_Commands.CreateAndOpenDreamerScheme) {
      this.createAndOpenDreamerScheme();
    } else if (cmd_name === LayoutAI_Commands.RenderAndOpenPanorama) {
      this.renderAndOpenPanorama();
    }
  }
  onkeydown(ev: KeyboardEvent): boolean {
    super.onkeydown(ev);
    if (ev.ctrlKey && ev.key == 'j') {
      this.openSwjXmlSchemeJson();
    }
    if (ev.key == 'Delete') {
      console.log('delete...');
      this._cad_default_sub_handler.deleteElement();
    }
    if (ev.ctrlKey && ev.key == 'v') {
      this._cad_default_sub_handler.copySelectedTarget();
    }

    if (ev.ctrlKey && ev.key === 'F2') {
      this.EventSystem.emit(EventName.setIssueReportVisible, true);
    }
    if (ev.ctrlKey && ev.key === 'F3') {
      this.EventSystem.emit(EventName.setIssueReportVisible, false);
    }
    if (ev.ctrlKey && ev.key === 'F10') {
      TLayoutParamConfigurationManager.instance.update();
    }
    if (ev.ctrlKey && ev.shiftKey && ev.altKey && ev.key === 'P' && ENV != 'prod') {
      localStorage.setItem('enable_sensors_log_print', 'true');
    }
    if (ev.ctrlKey && ev.shiftKey && ev.altKey && ev.key === 'Q' && ENV != 'prod') {
      localStorage.setItem('enable_sensors_log_print', 'false');
    }
    return true;
  }
  onkeyup(ev: KeyboardEvent): boolean {
    super.onkeyup(ev);
    return true;
  }

  operateUndoRedo(): void {
    this._cad_default_sub_handler.cleanSelection();
    this._cad_default_sub_handler.updateCandidateRects();
  }

  undo(): void {
    this.operateUndoRedo();
  }

  redo(): void {
    this.operateUndoRedo();
  }
  reset() {
    this.manager.layout_container.fromXmlSchemeData(
      this._initial_scheme_data as any as I_SwjXmlScheme
    );
    this._cad_default_sub_handler.cleanSelection();
    this._cad_default_sub_handler.updateCandidateRects();
    this.update();
  }
}
