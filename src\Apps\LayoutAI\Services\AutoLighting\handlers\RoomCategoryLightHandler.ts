import { <PERSON>L<PERSON>Handler } from "./BaseLightHandler";
import { LightingRuler, AutoLightingRulerType } from "../AutoLightingRuler";
import { TFillLightEntity } from "../../../Layout/TLayoutEntities/TFillLightEntity";
import { TFurnitureEntity } from "../../../Layout/TLayoutEntities/TFurnitureEntity";
import { TRoomEntity } from "../../../Layout/TLayoutEntities/TRoomEntity";
import { CategoryName } from "../../../Scene3D/NodeName";
import { ZRect } from "../../../ZPolygon/ZRect";
import { TBaseRoomToolUtil } from "../../../Layout/TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";
import { TFigureElement } from "../../../Layout/TFigureElements/TFigureElement";

export class RoomCategoryLightHandler extends BaseLightHandler {
    async handle(ruler: LightingRuler): Promise<TFillLightEntity[]> {
        console.log('RoomCategoryLightHandler handle_1', ruler);
        let entities: TFillLightEntity[] = [];
        let newRuler = { ...ruler };
        let layout_container = this.getLayoutContainer();
        layout_container._room_entities.forEach(roomEntity => {
            if (this.checkCondition(ruler, roomEntity)) {
                let furnitureList = roomEntity.getFurnitureEntitiesOnFlat();
                newRuler = this.checkLightRuler(ruler, furnitureList);
                furnitureList.forEach(furniture => {
                    let categoryList = (newRuler.category as string).trim().split('|');
                    if (categoryList.includes(furniture.category)) {
                        console.log('RoomCategoryLightHandler handle_2', furniture.category);
                        let entity = this.createCategoryLight(newRuler, furniture, furnitureList, roomEntity);
                        if (entity) {   
                            entities.push(entity);
                        } else {
                            console.log('RoomCategoryLightHandler handle_3', 'entity is null', ruler);
                        }
                    }
                });
            }
        });
        return entities;
    }

    private checkLightRuler(ruler: LightingRuler, furnitureList: TFurnitureEntity[]): LightingRuler {
        let newRuler = { ...ruler };
        
        // 沙发测光特殊规则
        if (ruler.typeId === AutoLightingRulerType.SofaSideLight) {
            let isTeapoy = furnitureList.some(furniture => furniture.category === ruler.category);
            newRuler = !isTeapoy ? 
                { ...ruler, category: CategoryName.Sofa, pose: { ...ruler.pose, gapOffset: 2000 } } : 
                ruler;
        }
        return newRuler;
    }
    

    private createCategoryLight(ruler: LightingRuler, furniture: TFurnitureEntity, furnitureList: TFurnitureEntity[], roomEntity?: TRoomEntity): TFillLightEntity {
        let rect = furniture.rect;
        let newRuler = { ...ruler }
        let lightWidth = this.getSize(newRuler.lighting.width, rect.h);
        let lightLength = this.getSize(newRuler.lighting.length, rect.w);
        // 家具侧光防止光源溢出/重叠
        if (ruler.pose.lookAt && ruler.pose.gapOffset) {
            let roomPoly = roomEntity._room_poly;
            let frontEdge = rect.frontEdge;
            let minDistance: number = Infinity;
            let rayStart = frontEdge.unprojectEdge2d({ x: frontEdge.length / 2, y: 0 });
            let rayDirection = frontEdge.nor.clone();

            // 房间空间（墙）
            let intersectionRoom = roomPoly.getRayIntersection(rayStart, rayDirection);
            if (intersectionRoom && intersectionRoom.point) {
                let distanceToRoom = intersectionRoom.point.clone().sub(rayStart).length();
                minDistance = Math.min(minDistance, distanceToRoom);
            }

            // 其他家具模型
            let currentLightPos = this.getLightPos(rect, newRuler);
            let currentLightRotation = this.getLightRotation(rect, newRuler, currentLightPos);
            let lightRect = new ZRect(lightLength, lightWidth);
            lightRect.rect_center = currentLightPos;
            lightRect.rotation_z = currentLightRotation.z;
            // 设置光源的Z轴位置，用于3D重叠检测
            lightRect.zval = currentLightPos.z - lightWidth / 2; // 光源底部Z坐标
            // 创建临时的光源TFigureElement用于3D重叠检测
            let lightFigure = new TFigureElement({
                category: "Light",
                sub_category: "Light",
                params: {
                    length: lightLength,
                    depth: lightWidth,
                    height: lightWidth // 光源的厚度作为高度
                }
            });
            lightFigure.rect.copy(lightRect);
            lightFigure.rect.zval = currentLightPos.z - lightWidth / 2; // 光源底部Z坐标

            furnitureList.forEach(otherFurniture => {
                if (otherFurniture.category === ruler.category && otherFurniture.pos_z + otherFurniture.height / 2 < currentLightPos.z - lightWidth / 2) {
                    console.log('跳过:', otherFurniture.category)
                    return;
                };

                // 使用项目中封装好的3D重叠检测方法
                // isOverlayByFigures 方法会检测两个TFigureElement是否有任何3D重叠（包括部分重叠）
                let isOverlapping = TBaseRoomToolUtil.instance.isOverlayByFigures(lightFigure, otherFurniture.figure_element, false);

                if (isOverlapping) {
                    let intersectionFurniture = otherFurniture.rect.getRayIntersection(rayStart, rayDirection);
                    if (intersectionFurniture && intersectionFurniture.point) {
                        let distanceToFurniture = intersectionFurniture.point.clone().sub(rayStart).length();
                        console.log(minDistance, distanceToFurniture);
                        minDistance = Math.min(minDistance, distanceToFurniture);
                        // 计算重叠面积用于调试
                        let overlapArea = TBaseRoomToolUtil.instance.calOverlayRatioForRects(lightRect, otherFurniture.rect) * Math.min(lightRect.area, otherFurniture.rect.area);
                        console.log('光源矩形与家具重叠:', otherFurniture.category, '重叠面积:', overlapArea, '光源矩形:', lightRect.area, '家具矩形:', otherFurniture.rect.area);
                    }
                }
            });
            if (minDistance < ruler.pose.gapOffset) {
                newRuler = { ...ruler, pose: { ...ruler.pose, gapOffset: minDistance - 10 } };
            }
        }
        let pos = this.getLightPos(rect, newRuler);
        let rotation = this.getLightRotation(rect, newRuler, pos);
        let entity = this._createFillLightEntity(
            newRuler.lighting.intensity,
            newRuler.lighting.color,
            { x: pos.x, y: pos.y, z: pos.z },
            lightWidth,
            lightLength,
            rotation.x,
            rotation.y,
            rotation.z,
            newRuler.name,
        );
        return entity;
    }
}