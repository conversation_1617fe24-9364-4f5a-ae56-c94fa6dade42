import React, { useEffect, useState, Suspense } from 'react';
import { Route, Routes, Navigate, BrowserRouter } from 'react-router-dom';
import { is_standalone_website, checkIsMobile, scheme_Id, _from, MobileConfig, isLocal3d, mode_type, is_dreamer_mini_App } from '@/config';
import './utils/browserPatch';
import { ConfigProvider, getSvgTokenConfig } from '@svg/antd';
import type { ConfigProviderProps } from '@svg/antd';
import { SSOPlug } from '@/utils/sso-plug';

import loadLanguageAsync from '@/locales/index';
import { useStore } from '@/models';
import { getModule, getTokenByCode, getUserInfo, getVrUserInfo, listAuthorizationCode } from '@/services/user';
import SunvegaAPI from '@api/clouddesign'; // 云设计APISDK包
import i18n from "i18next";
import { observer } from "mobx-react-lite";
import { I18nextProvider } from "react-i18next";
import DreamerTrainingPage from './pages/Training/dreamer_training';
import { isMobile, isTablet } from 'react-device-detect';
import { magiccubeRequest, openApiRequest } from './utils';
import { openGatewayUrl } from '@/config/host';
import { LayoutAI_App } from './Apps/LayoutAI_App';
import { EventName } from './Apps/EventSystem';
const is_local3d_debug = window.location.hostname.includes('local3d');
// 重载是否是移动端的全局配置
MobileConfig.func_checkIsMobile = ()=>{
  const userAgent = navigator.userAgent.toLowerCase();
  const mobileKeywords = ['android', 'iphone', 'ipad', 'ipod', 'windows phone'];
  const isMobileUserAgent = mobileKeywords.some(keyword => userAgent.includes(keyword));
  const isMobileScreen = window.matchMedia("(max-width: 767px)").matches;
  // 由于搭载 M 系芯片的 iPad 和 Mac 的关键在于 User Agent（UA）中不再包含 “iPad” 而是显示为 “Macintosh” 和Mac一样
  // 因此需要加上是否支持触摸
  const isMacintosh = userAgent.includes("macintosh");
  // 检测触摸支持（iPad通常支持多点触控）
  const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 1;

  return isMobileUserAgent || isMobileScreen || isMobile || isTablet || isMacintosh && isTouch
}
checkIsMobile(true);


SSOPlug.init();
// console.log('process.env.NODE_ENV', process.env.NODE_ENV);


window.addEventListener('resize', () => {
  document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);
  checkIsMobile(true);
});
/**
 * @description 获取基础路由
 */
const getBasename = () => {
  if (process.env.NODE_ENV === 'development') {
    return '';
  }
  const pathname = window.location.pathname || '';
  if (window.location.hostname?.indexOf('miniapp') > -1) {
    const pathnameArr = pathname.split('/');
    return pathnameArr[1];
  }
  return pathname.substring(1, pathname.lastIndexOf('/'));
}

// 使用 React.lazy 异步加载组件
const Home = React.lazy(() => import('@/pages/Home/home'));
const Design = React.lazy(() => import('@/pages/Design/design'));
const TrainingPage = React.lazy(() => import('@/pages/Training/training'));
const DreamhouseHome = React.lazy(() => import('@/pages/Home/dreamhouseHome'));
const LightMobile = React.lazy(() => import('@/pages/LightMobile/lightMobile'));
const LightHuawei = React.lazy(() => import('@/pages/LightMobile/lightMain/lightHuawei'));
const ShareHome = React.lazy(() => import('@/pages/New3d/shareHome'));
const Mobile = React.lazy(() => import('@/pages/Mobile/mobile'));
const Websocket = React.lazy(() => import('@/pages/WebSocket'));
const AiDraw = React.lazy(() => import('@/pages/AiDraw'));
const Trial = React.lazy(() => import('@/pages/Trial/trial'));
const AiLight = React.lazy(() => import('@/pages/AiLight'));
const PadMobile = React.lazy(() => import('@/pages/Mobile/padMobile'));
const SdkFrame = React.lazy(()=>import('@/pages/SdkFrame/SdkFrame'));
const New3d = React.lazy(()=>import('@/pages/New3d/home'));
function App() {
  const { Common } = SunvegaAPI;
  let store = useStore();
  let {setSunDEnterOpen, setShowWelcomePage, setMenuKey} = store.homeStore;
  const basename = getBasename();
  const [appearance, setAppearance] = useState<any>('light');
  const [token, setToken] = useState<ConfigProviderProps>(getSvgTokenConfig('light'));
  const [isLanguageLoaded, setIsLanguageLoaded] = useState(false);

  const setWelcomePage = async () => {
    const houseStrutureResponse = await SunvegaAPI.BasicBiz.HouseApi.getHouseStructure();
    if(is_dreamer_mini_App)
    {
      return;
    }
    else if(!is_standalone_website)
    {
      if(houseStrutureResponse?.data?.house?.room_list.length == 0)
        {
          setShowWelcomePage(true);
          setSunDEnterOpen(false);
        } else 
        {
          setSunDEnterOpen(true);
        }
    }
    if(is_standalone_website && !scheme_Id && !mode_type)
    {
      setShowWelcomePage(true);
    }
    if(is_standalone_website && mode_type === 'HouseId')
    {
      setSunDEnterOpen(true);
    }

    if (houseStrutureResponse?.data?.house?.room_list.length > 0 || scheme_Id || _from === 'local') return;
    if (is_local3d_debug) return;
    if (store.userStore.aihouse)
    {
      setShowWelcomePage(true);
      setMenuKey('我的方案');
      return;
    }
  }

  const setEnterPage = () => {
    // 进入的时候，如果 mode_type 为 schemecreate 或者没有 mode_type 或者没有 scheme_Id，则显示进入流程的弹窗
    if (localStorage) {
      const debug = localStorage.getItem('LayoutAI_Debug');
      if(debug == '1') return;
    }
    if(checkIsMobile() || window.location.pathname.includes('/padmobile'))
    {
      if(scheme_Id) return;
      store.homeStore.setShowEnterPage({show: true, source: 'App'});
    }
  }

  const checkAuth = async () => {
    try {
      const authList = await listAuthorizationCode();
      // 这里假设 authList 是一个包含权限代码的数组，你需要根据实际情况修改这部分的代码
      if (authList) {
        if (authList.findIndex((authoCodeObj: any) => authoCodeObj.operationId === '13019081') !== -1) {
          store.userStore.setHasAuth(true)
        } else {
          store.userStore.setHasAuth(false)
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    const handleWheel = (e: any) => {
      if (e.ctrlKey) {
        e.preventDefault();
      }
    };

    window.addEventListener('wheel', handleWheel, { passive: true });


    return () => {

      window.removeEventListener('wheel', handleWheel);
    };
  }, []);

  const getUser = async () => {
    try {
      const res = await getUserInfo();
      const VRres = await getVrUserInfo();
      const prams = {
        ...res,
        ...VRres,
      }
      store.userStore.setUserInfo(prams);
      const isAihosue = prams?.regSource === 'aihouse' || window.location.hostname.indexOf('aihouse') > -1
      store.userStore.setAihouse(isAihosue);
      setWelcomePage();
      setEnterPage();
    } catch (error) {
      setIsLanguageLoaded(true);
      console.error(error);
    }
  };

  const getModuleSettings = async () => {
    try {
      const res = await getModule();
      store.userStore.setBeta(res?.internaltesting === '1' ? true : false)
    } catch (error) {
      console.error(error);
    }
  }

  const loadLanguage = async () => {
    try {
      const { data } = await Common.Framework.getLocale();
      if (data && data?.lang !== 'zh-CN') {
        store.userStore.setAihouse(true);
      }
      await loadLanguageAsync(data?.lang || 'zh-CN');
    } catch (error) {
      setIsLanguageLoaded(true);
      console.error(error);
    }
  };
  let targetRoute = "/";
  if (window.location.href && window.location.href.includes("WebSocket")) {
    targetRoute = "WebSocket";
  }
  if (window.location.href && window.location.href.includes("Training")) {
    targetRoute = "Training";
  }
  
  useEffect(() => {
    const initialize = async () => {
      const isShareRoute = window.location.pathname.includes('/share') || window.location.pathname.includes('/sdk');
      const isTrial = window.location.pathname.includes('/trial')
      const params = new URLSearchParams(window.location.search);
      const code = params.get('code');
      const appId = params.get('appId');
      // 如果地址有code和appId，走open gateway 模式登录获取用户信息模式
      if(code && appId)
      {
        const data= await getTokenByCode({
          appId,
          code: code,
        });
        if(data?.data?.jointToken){
          sessionStorage.setItem('jointToken', data.data.jointToken);
          magiccubeRequest.defaults.baseURL = openGatewayUrl;
          magiccubeRequest.defaults.headers['Joint-Token'] = data.data.jointToken || undefined;
          magiccubeRequest.defaults.headers['Magiccube-Token'] = data.data.jointToken || undefined;
          openApiRequest.defaults.baseURL = openGatewayUrl;
          openApiRequest.defaults.headers['Joint-Token'] = data.data.jointToken || undefined;
          openApiRequest.defaults.headers['Magiccube-Token'] = data.data.jointToken || undefined;
        }
      }
      if (!isShareRoute) {
        // 分享页不调用接口，去除登录校验+
        await checkAuth();
      }
      // 独立站点
      if (isShareRoute || isTrial) {
        setIsLanguageLoaded(true);
        return;
      }
      
      // 获得用户信息
      await getUser();

      if (is_standalone_website) // 独立站点: 异步加载语言包
      {
        const isAihosue = store.userStore.userInfo?.regSource === 'aihouse' || window.location.hostname.indexOf('aihouse') > -1
        store.userStore.setAihouse(isAihosue);
        loadLanguageAsync(null, isAihosue);
      } 
      else 
      {
        // 从3D进来, 使用3D的本地信息
        await loadLanguage();
      }
      setIsLanguageLoaded(true);
      getModuleSettings();
    }

    initialize();
  }, [])

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.code === 'Digit9') {
        if (appearance === 'light') {
          setToken(getSvgTokenConfig('dark'));
          setAppearance('dark');
        } else {
          setToken(getSvgTokenConfig('light'));
          setAppearance('light');
        }

      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [appearance]);

  return (
      <div className="App">
        <I18nextProvider i18n={i18n}>
          <ConfigProvider {...token}>
            <BrowserRouter basename={basename}>
              <Suspense fallback={<div></div>}>
                {isLanguageLoaded ? (
                  <Routes>
                    <Route path="*" element={<Navigate to={targetRoute} />}></Route>
                    (
                    <>
                      {/* <Route path="/" element={<Welcome />}/> */}
                      <Route path="/" element={!checkIsMobile() ? <Home /> : <PadMobile />} />
                      <Route path="/home" element={!checkIsMobile() ? <Home /> : <PadMobile />} />
                      <Route path="/pchome" element={<Home />} />
                      <Route path="/Dreamer" element={!checkIsMobile() ? <DreamhouseHome /> : <PadMobile />} />
                      {/* <Route path="/Layout" element={<AuthRoute> {<Layout />} </AuthRoute>} /> */}
                      <Route path="/Design" element={<Design />} />
                      <Route path="/Training" element={<TrainingPage />} />
                      <Route path="/DreamerTraining" element={<DreamerTrainingPage />} />
                      <Route path="/lightMobile" element={<LightMobile />} />
                      <Route path="/padMobile" element={<PadMobile />} />
                      <Route path="/lightHuawei" element={<LightHuawei />} />
                      <Route path="/share" element={<ShareHome />} />
                      <Route path="/mobile" element={<PadMobile />} />
                      <Route path="/MyMobile" element={<Mobile />} />
                      <Route path="/aidraw" element={<AiDraw modelType={false} />} />
                      <Route path="/trial" element={<Trial />} />
                      {/* <Route path="/ailight" element={<AiLight modelType={false} />} /> */}
                      <Route path="/sdkFrame" element={<SdkFrame />}/>
                      <Route path="/new3d" element={<New3d />}/>
                    </>
                    )
                  </Routes>
                ) : (
                  <div></div>
                )}
              </Suspense>
            </BrowserRouter>
          </ConfigProvider>
        </I18nextProvider>
      </div>
  );
}

export default observer(App);
