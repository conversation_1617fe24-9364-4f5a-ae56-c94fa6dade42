"use strict";(self.webpackChunkai_design_plugin=self.webpackChunkai_design_plugin||[]).push([[700],{35700:function(n,e,t){t.d(e,{A:function(){return Lt}});var r=t(13274),i=t(61643),o=t(81639);function a(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function l(){var n=a(["\n      position:fixed;\n      background : #fff;\n      z-index: 10;\n      .closeBtn {\n        display:none;\n        position:absolute;\n         right : 6px;\n         top : 6px;\n         font-size:20px;\n         width:60px;\n         height : 24px;\n         text-align:right;\n      }\n      &.panel_hide {\n        box-shadow: 0px 0px 0px 0px #00000000;\n      }\n      @media screen and (orientation: landscape) {\n        position:fixed;\n        left: 12px !important;\n        top: 52px !important;\n        bottom: 12px !important;\n        right: auto !important;\n        height: auto;\n        padding-left: 0 !important;\n        max-height: calc(var(--vh, 1vh) * 100);\n        max-width:224px;\n        width: 224px;\n        border-radius: 8px;\n        box-shadow:  0px 0px 16px 10px #0000000A;\n        &.panel_hide {\n          display: none;\n        }\n      }\n      @media screen and (orientation: portrait) {\n        position:fixed;\n        left:0;\n        bottom:0px;\n        right:0;\n        width : auto;\n        height:340px;\n        max-width : auto;\n        max-height:340px;\n        overflow: hidden;\n        background-color: #fff;\n        border-radius: 8px 8px 0px 0px;\n        box-shadow:  0px 0px 16px 10px #0000000A;\n        @media screen and (-webkit-min-device-pixel-ratio:2) and (max-height:700px) {\n          transform : scale(0.7);\n          transform-origin : bottom;\n          left : -15%;\n          right : -15%;\n        }\n        &.panel_hide {\n          max-width : 0px;\n        }\n        .closeBtn {\n          display : block;\n        }\n      }\n\n\n      .fade-enter {\n  opacity: 0;\n}\n\n.fade-enter-active {\n  opacity: 1;\n  transition: opacity 300ms ease-in-out;\n}\n\n.fade-exit {\n  opacity: 1;\n}\n\n.fade-exit-active {\n  opacity: 0;\n  transition: opacity 300ms ease-in-out;\n}\n    "]);return l=function(){return n},n}function c(){var n=a(["\n      background: rgba(0, 0, 0, 0.40) !important;\n      backdrop-filter: blur(50px) !important;\n    "]);return c=function(){return n},n}function s(){var n=a(["\n      height: 60px;\n      width : 100%;\n      @media screen and (orientation: landscape) {\n        height: 55px;\n        width : 100%;\n        color :#000;\n        font-weight:700;\n        padding-left: 13px;\n        padding-right: 13px;\n        padding-top: 12px;\n        display:block;\n\n      }\n      @media screen and (orientation: portrait) {\n        width: 30%;\n        margin-top: 10px;\n        margin-left: 10px;\n        height: auto;\n      }\n    "]);return s=function(){return n},n}function u(){var n=a(["\n        position: absolute;\n        left: 5px;\n        width: 34px;\n        top: 50px;\n        height:210px;\n        display: flex;\n        flex-direction: column;\n        font-size: 17px;\n        align-items: center;\n        flex-wrap: nowrap;\n        justify-content: center;\n        border-radius:8px;\n        color : #aaa;\n        text-align:center;\n        background:#eee;\n\n        @media screen and (orientation: landscape) {\n            display:none;\n        }\n        @media screen and (orientation: portrait) {\n          display: flex;\n          \n        }\n    }\n      .vTab {\n        width: 30px;\n        padding-top: 8px;\n        padding-bottom: 8px;\n        padding-left:5px;\n        padding-right:5px;\n\n        &.checked {\n          background:#fff;\n          color : #2b2b2b;\n          border-radius:8px;\n\n        }\n\n      }\n    "]);return u=function(){return n},n}function d(){var n=a(["\n      display:none;\n      width: 20px;\n      height: 48px;\n      line-height: 48px;\n      text-align: center;\n      background-color: #fff;\n      border-radius: 0px 6px 6px 0px;\n      box-shadow: 0px -16px 16px 0px #00000005;\n      cursor:pointer;\n\n      @media screen and (orientation: landscape) {\n        display:block;\n        position: fixed;\n        left: 235px;\n        top: calc(50% - 48px);\n        z-index: 9;\n      }\n      @media screen and (orientation: portrait) {\n        position: fixed;\n        bottom: 120px;\n        left: 0px;\n        z-index: 999;\n\n      }\n      &.panel_hide {\n        left:0px;\n        display:block;\n      }\n    "]);return d=function(){return n},n}function f(){var n=a(["\n      color : #959598;\n      .ant-tabs-tab {\n        color :#959598;\n      }\n    "]);return f=function(){return n},n}function p(){var n=a(["\n      display: flex;\n      height: 40px;\n      padding: 0 24px;\n      align-items: center;\n      font-size: 20px;\n      color: #282828;\n      font-weight: 600;\n      margin-top: 16px;\n      justify-content: space-between;\n      @media screen and (max-width: 450px) { // 手机宽度\n        height: 15px;\n        font-size: 16px;\n      }\n      @media screen and (orientation: landscape) {\n        height: 40px;\n        font-size: 14px;\n      }\n\n    "]);return p=function(){return n},n}function h(){var n=a(["\n      /* position:absolute;\n      left:45px;\n      right:5px;\n      top: 0px;\n      bottom:10px;\n      overflow:hidden;\n      @media screen and (orientation: landscape) {\n        left:5px;\n        top: 0px;\n\n      } */\n    "]);return h=function(){return n},n}function x(){var n=a(["\n      height:100%;\n      width:100%;\n    "]);return x=function(){return n},n}function m(){var n=a(["\n      position: absolute; \n      right: 10px;\n      top: 10px;\n      z-index: 9;\n    "]);return m=function(){return n},n}function b(){var n=a(["\n    /* position: fixed;\n    top: 0px;\n    background-color: #fff;\n    width: 100%;\n    height: 56px;\n\n    display: flex;\n    padding: 0 16px;\n    justify-content: space-between;\n    align-items: center;\n    z-index: 9;\n    max-width: 1174px;\n    @media screen and (max-width: 450px) { // 手机宽度\n      height: 46px;\n    } */\n  "]);return b=function(){return n},n}function g(){var n=a(["\n    position: fixed;\n    top: 12px;\n    left: 12px;\n    height: 32px;\n    width: 68px;\n    line-height:28px;\n    font-size: 14px;\n    background:#fff;\n    border-radius:6px;\n    text-align:center;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    .iconleft {\n      text-align:left;\n    }\n    @media screen and (max-width: 450px) { // 手机宽度\n      font-size: 12px;\n      width:50px;\n\n    }\n    z-index: 9;\n  "]);return g=function(){return n},n}function v(){var n=a(["\n    background: rgba(0, 0, 0, 0.40);\n    backdrop-filter: blur(50px);\n    color: #fff;\n  "]);return v=function(){return n},n}function y(){var n=a(["\n    position: fixed;\n    top: 12px;\n    right: 12px;\n    height : 28x;\n    width:60px;\n    line-height:28px;\n    color: #fff;\n    font-size: 13px;\n    border-radius:6px;\n    text-align:center;\n    background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n    z-index:9;\n    @media screen and (max-width: 450px) { // 手机宽度\n      font-size: 12px;\n    }\n  "]);return y=function(){return n},n}function w(){var n=a(["\n      position: fixed;\n      top: 6px;\n      right: 12px;\n      width:40px;\n      height : 40px;\n      line-height:40px;\n      border-radius:6px;\n      text-align:center;\n      color :#282828;\n      background:rgba(255,255,255,0.2);\n      z-index:9;\n      font-size: 16px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        font-size: 14px;\n      }\n\n   "]);return w=function(){return n},n}function j(){var n=a(["\n    top: 50px;\n    right: 12px;\n    position: fixed;\n    z-index:999;\n    background:#fff;\n    padding:10px;\n    border-radius:6px;\n  "]);return j=function(){return n},n}function S(){var n=a(["\n      position:fixed;\n      width: 200px;\n      left: calc(50% - 100px);\n      top: 0px;\n      @media screen and (max-width: 450px) { // 手机宽度\n        width:120px;\n        left: calc(50% - 60px);\n      }\n      @media screen and (max-width: 350px) { // 手机宽度\n        top: 60px;\n        left :12px;\n      }\n      z-index: 9;\n    "]);return S=function(){return n},n}function _(){var n=a(["\n        position:fixed;\n        width: 200px;\n        right: 12px;\n        top: 5px;\n        z-index: 9;\n    "]);return _=function(){return n},n}function A(){var n=a(["\n      position:fixed;\n      bottom:13px;\n      z-index:9;\n      display:flex;\n      justify-content:center;\n      align-items:center;\n      left: 50%;\n      transform: translate(-50%, 0);\n      transition: all 0.5s ease;\n      .btn {\n        border-radius: 50px;\n        background: #FFFFFF;\n        box-shadow: 0px 6px 20px 0px #00000014;\n        width : 140px;\n        border: none;\n        height : 48px;\n        color: #282828;\n        font-size: 14px;\n        line-height: 48px;\n        letter-spacing: 0px;\n        text-align: center;\n        margin-left:12px;\n        margin-right:12px;\n      }\n      .blackColor {\n        background: rgba(0, 0, 0, 0.40) !important;\n        backdrop-filter: blur(50px) !important;\n        color: #fff !important;\n      }\n      @media screen and (orientation: landscape){\n        display:flex;\n        justify-content:center;\n        align-items:center;\n        left: 50%;\n        transform: translate(-50%, 0);\n      }\n      &.showLeftPanel {\n        @media screen and (orientation: portrait) {\n          position: fixed;\n          bottom: 280px;\n          max-width : 48px;\n          top : auto;;\n          left: 44%;\n          transform: translateX(-50%);\n          right : 24px;\n          height: 120px;\n          display: block;\n\n          @media screen and (max-width: 450px) { // 手机宽度\n            right : 12px;\n          }\n          @media screen and (max-height: 700px) { \n            right : auto;\n            left : 0px;\n          }\n          .btn {\n            border-radius: 50px;\n            background: #FFFFFF;\n            box-shadow: 0px 6px 20px 0px #00000014;\n            width : 140px;\n            border: none;\n            height : 48px;\n            color: #282828;\n            font-size: 14px;\n            line-height: 48px;\n            letter-spacing: 0px;\n            text-align: center;\n            margin-left:12px;\n            margin-right:12px;\n  \n            &.hasIcon {\n              line-height:19px;\n              .iconfont {\n                  display:block;\n                  margin-top:4px;\n              }\n            }\n            @media screen and (max-width: 450px) { // 手机宽度\n              width: 40px !important;\n              height: 44px !important;\n              font-size: 10px !important;\n            }\n          }\n      }\n\n\n      }\n    "]);return A=function(){return n},n}function C(){var n=a(["\n      position:fixed;\n      right:0;\n      z-index:9;\n      top:0px;\n      transition: all 0.2s ease;\n      &.is_3d_mode {\n        top:180px;\n      }\n    "]);return C=function(){return n},n}function k(){var n=a(["\n    width:100%;\n    font-size:16px;\n    line-height:40px;\n    text-align:center;\n  "]);return k=function(){return n},n}var I=(0,o.rU)((function(n){var e=n.css;return{leftPanelRoot:e(l()),materialReplace:e(c()),tabBar:e(s()),leftTabBar:e(u()),collapseBtn:e(d()),tab_root:e(f()),topTitle:e(p()),popupContainer:e(h()),listContainer:e(x()),open:e(m()),navigation:e(b()),backBtn:e(g()),blackColor:e(v()),forwardBtn:e(y()),closeBtn:e(w()),shareBarContainer:e(j()),topTabs:e(S()),rightSceneModeTabs:e(_()),bottomButtons:e(A()),sideToolbarContainer:e(C()),schemeNameSpan:e(k())}})),M=t(15696),z=t(41594),N=t(27347),D=t(45599),E=t(50617),P=t(70060),F=t(76135),B=t(93491),O=t(70524),R=t(81074),T=t(71195),L=t(23825),U=t(88934),W=t(32184),H=t(67869),V=t(7224),$=t(73062),G=t(23184),q=t(49063),K=t(90803),Y=t(14181),Z=t(99030),X=t(61928);function J(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Q(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function nn(n){return function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){Q(o,r,i,a,l,"next",n)}function l(n){Q(o,r,i,a,l,"throw",n)}a(void 0)}))}}function en(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function tn(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),r.forEach((function(e){en(n,e,t[e])}))}return n}function rn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return J(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return J(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function on(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var an=(0,M.observer)((function(){(0,i.B)().t;var n=(0,K.A)().styles,e=(0,O.P)(),t=rn((0,z.useState)({left:"0",top:"0",width:"0",height:"0",border:"0px solid #ffffea",transition:"all 0.3s ease"}),2),o=t[0],a=t[1],l=N.nb.instance.layout_container,c=function(){return nn((function(){var n,t,r,i,o,a,c,s;return on(this,(function(u){switch(u.label){case 0:return i=N.nb.instance.scene3D,o=(null===(n=e.homeStore.guideMapCurrentRoom)||void 0===n?void 0:n.name)||(null===(t=l._selected_room)||void 0===t?void 0:t.roomname)||"",a=e.homeStore.guideMapCurrentRoom||l._selected_room||null,c=3,a&&a.furnitureList&&a.furnitureList.length>0&&(c=0),s=!1,(null===(r=i.selection_box)||void 0===r?void 0:r.visible)&&(s=i.selection_box.visible,i.selection_box.visible=!1),i.update(),[4,Y.w.instance.submitAiDraw({room_name:o,roomUid:(null==a?void 0:a.uid)||"",aiModel:c},e.homeStore.aspectRatioMode)];case 1:return u.sent(),s&&(i.selection_box.visible=s),[2]}}))}))()};return(0,z.useEffect)((function(){N.nb.on_M(Z.r.AiDrawingCapture,"AiDrawingGallery",(function(){var n=N.nb.instance.scene3D,e=(N.nb.instance.layout_container,{left:"0",top:"0",width:"100vw",height:"100vh",transition:"none"}),t={left:"0",top:"0",width:"0",height:"0",border:"0px solid #ffffea",transition:"all 0.3s ease"};if(nn((function(){var n;return on(this,(function(e){switch(e.label){case 0:return n=null,l._layout_scheme_id?[3,1]:(N.nb.DispatchEvent(N.n0.autoSave,null),n&&clearInterval(n),n=setInterval((function(){return nn((function(){return on(this,(function(e){switch(e.label){case 0:return l._layout_scheme_id?(clearInterval(n),n=null,[4,c()]):[3,2];case 1:e.sent(),e.label=2;case 2:return[2]}}))}))()}),500),[2]);case 1:return[4,c()];case 2:e.sent(),e.label=3;case 3:return[2]}}))}))(),n.parent_div){var r=n.parent_div.offsetLeft,i=n.parent_div.offsetTop,o=n.parent_div.offsetWidth,s=n.parent_div.offsetHeight;e.left=r+"px",e.top=i+"px",e.width=o+"px",e.height=s+"px"}var u=document.getElementById("aidrawing_tuku_btn");if(u&&u.getBoundingClientRect){var d=u.getBoundingClientRect();if(d){var f=d.left,p=d.top;f+=d.width/2,p+=d.height/2,t.left=f+"px",t.top=p+"px"}}a(tn({},e)),setTimeout((function(){a(tn({},t))}),300)})),N.nb.on_M(U.U.diffuseImage,"aiDrawingGallery",(function(n){var t;X.K.diffuseImageSDK(n,null===(t=e.homeStore.guideMapCurrentRoom)||void 0===t?void 0:t.roomname)}))}),[]),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:n.photo_capture_div+" photo_capture_div",style:o})})})),ln=t(84749),cn=t(79324),sn=t(78154),un=t(76330),dn=t(65640);function fn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function pn(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function hn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||mn(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xn(n){return function(n){if(Array.isArray(n))return fn(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||mn(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function mn(n,e){if(n){if("string"==typeof n)return fn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?fn(n,e):void 0}}function bn(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}cn.A.confirm;var gn=(0,M.observer)((function(){var n,e,t,o=(0,i.B)().t,a=(0,ln.A)().styles,l=(0,O.P)(),c=hn((0,z.useState)([]),2),s=(c[0],c[1]),u=hn((0,z.useState)(!1),2),d=u[0],f=u[1],p=hn((0,z.useState)("null"),2),h=p[0],x=p[1],m=(N.nb.instance.layout_container,hn((0,z.useState)([]),2)),b=m[0],g=m[1],v=hn((0,z.useState)(0),2),y=v[0],w=v[1],j=hn((0,z.useState)(""),2),S=(j[0],j[1],hn((0,z.useState)(!1),2)),_=(S[0],S[1]),A=hn((0,z.useState)(!1),2),C=A[0],k=A[1],I=hn((0,z.useState)(!1),2),M=I[0],D=(I[1],hn((0,z.useState)({designStyle:[],rooms:[]}),2)),E=(D[0],D[1]),P=((0,q.Zp)(),d?36:45),F=[{id:"Layout",label:o("布局"),icon:"icon-a-TypebujuStateDefault"},{id:"Matching",label:o("风格"),icon:"icon-a-TypefenggeStateDefault"},"2D"===l.homeStore.viewMode?{id:"material",label:o("素材"),icon:"icon-a-TypesucaiStateDefault"}:null,{id:"attribute",label:o("属性"),icon:"icon-a-TypeshuxingStateDefault"},"3D_FirstPerson"===l.homeStore.viewMode?{id:"view",label:o("视角"),icon:"icon-smarttemplate"}:null,"2D"===l.homeStore.viewMode?{id:"aidraw",label:o("AI绘图"),icon:"icon-AIchutu"}:null,L.Ic?{id:"similar",label:o("相似匹配"),icon:"icona-Typexuanzebujian"}:null,L.Ic?{id:"create",label:o("新建"),icon:"iconfile"}:null].filter(Boolean),B=[{id:"Layout",label:o("布局"),icon:"icon-a-TypebujuStateDefault"},{id:"Matching",label:o("风格"),icon:"icon-a-TypefenggeStateDefault"},{id:"submitDrawing",label:o("提交绘图"),icon:"icon-xuanranRender"},{id:"atlas",label:o("图册"),icon:"icon-tuku"}].filter(Boolean),R=[{id:"size",icon:"icon-chicun",label:o("尺寸"),onClick:function(){l.homeStore.setSizeInfo({type:"size",visible:!0}),N.nb.emit_M(sn.$.showPopup,"sizeEditor")},disabled:!1,divider:!1},{id:"rotate",icon:"icon-rotate",label:o("旋转"),disabled:!1,divider:!1},{id:"flip",icon:"icon-horizontalflip_line",label:o("左右镜像"),disabled:!1,divider:!1},{id:"copy",icon:"icon-niantie",label:o("复制"),disabled:!1,divider:!1},"Furniture"===(null===(n=l.homeStore.selectEntity)||void 0===n?void 0:n.type)&&!(null===(e=l.homeStore.selectEntity)||void 0===e?void 0:e.figure_element.haveMatchedMaterial())&&{id:"pos_z",icon:"icon-lidi",label:o("离地"),disabled:!1,divider:!1},{id:"delete",icon:"icon-delete",label:o("删除"),disabled:!1,divider:!0}];R=R.filter(Boolean);var T=[{id:"rotate",icon:"icon-rotate",label:o("旋转"),disabled:!1,divider:!1},{id:"delete",icon:"icon-delete",label:o("删除"),disabled:!1,divider:!0}],H=[{id:"delete",icon:"icon-delete",label:o("删除"),disabled:!1,divider:!0}],V=[{id:"splitWall",icon:"iconsplit",label:o("拆分墙")},{id:"delete",icon:"icon-delete",label:o("删除"),disabled:!1,divider:!0}],$=[{id:"rotate",icon:"icon-rotate",label:o("旋转"),disabled:!1,divider:!1},{id:"ungroupTemplate",icon:"icon-jiezu-2",label:o("解组"),disabled:!1,divider:!1},{id:"delete",icon:"icon-delete",label:o("删除"),disabled:!1,divider:!0}],G=[{id:"attribute",label:o("属性"),icon:"icon-a-TypeshuxingStateDefault",onClick:function(){N.nb.emit_M(sn.$.showPopup,"attribute")}},{id:"material",label:o("清除布局"),icon:"icon-shanchubuju",EventName:"ClearLayout"},l.homeStore.isSingleRoom||"2D"!==l.homeStore.viewMode?null:{id:"focusSpace",label:o("专注空间"),icon:"icon-zhuanzhukongjian",EventName:"SingleRoomLayout",onClick:function(){N.nb.DispatchEvent(N.n0.SingleRoomLayout,l.homeStore.selectEntity),l.homeStore.setIsSingleRoom(!0)}},l.homeStore.isSingleRoom&&"2D"===l.homeStore.viewMode?{id:"exit",label:"",icon:"icon-a-tianchongFace-1",onClick:function(){N.nb.DispatchEvent(N.n0.leaveSingleRoomLayout,{}),l.homeStore.setIsSingleRoom(!1)}}:null];G=G.filter((function(n){return null!==n})),(0,z.useEffect)((function(){N.nb.on_M(U.U.SelectingTarget,"PadStatusBar",(function(n){var e,t=n||null;l.homeStore.setSelectEntity(n),n||l.homeStore.setShowReplace(!1);var r=(null==t?void 0:t.type)||null,i=(null==n||null===(e=n.ex_prop)||void 0===e?void 0:e.label)||null;if("Furniture"===r){var a=[];a=xn(R),(null==t?void 0:t.matched_rect)&&a.splice(a.length-1,0,{id:"replace",icon:"icon-change_logo",label:o("替换"),disabled:!1,divider:!1}),g(a)}else if("Group"===r){var c;c=R.concat([{id:"replace",icon:"icon-change_logo ",label:o("替换"),disabled:!1,divider:!1}]),g(c)}else if("BaseGroup"===r){var s=[];s=xn($),(null==t?void 0:t.matched_rect)&&s.splice(s.length-1,0,{id:"replace",icon:"icon-change_logo",label:o("替换"),disabled:!1,divider:!1}),g($)}else if("Door"===r||"Window"===r){if("baywindow"===i)return void g(H);g(T)}else"Wall"===r?g(V):"StructureEntity"===r?g([{id:"delete",icon:"icon-delete",label:o("删除"),disabled:!1,divider:!0}]):r===W.Fz.RoomSubArea&&g([{id:"roomSubAreaAttribute",icon:"icon-a-TypeshuxingStateDefault",label:o("属性"),disabled:!1,divider:!1,onClick:function(){N.nb.emit_M(sn.$.showPopup,"SpaceAreaAttribute")}},{id:"copyRoomSubArea",icon:"iconcopy",label:o("复制"),disabled:!1,divider:!0},{id:"deleteRoomSubArea",icon:"icon-delete",label:o("删除"),disabled:!1,divider:!0}])})),kn()}),[]),(0,z.useEffect)((function(){"3D_FirstPerson"===l.homeStore.viewMode?s(B):s(F)}),[l.homeStore.viewMode]),(0,z.useEffect)((function(){var n,e,t;if("3D_FirstPerson"===l.homeStore.viewMode&&"Furniture"!==(null===(n=l.homeStore.selectEntity)||void 0===n?void 0:n.type))return X(null),void N.nb.instance.layout_container.cleanDimension();if("3D_FirstPerson"===l.homeStore.viewMode&&"Furniture"===(null===(e=l.homeStore.selectEntity)||void 0===e?void 0:e.type))return N.nb.emit_M(sn.$.showPopup,"replace"),N.nb.instance.layout_container.cleanDimension(),Z(!1),void X(null);var r=(null===(t=l.homeStore.selectEntity)||void 0===t?void 0:t.type)||null;X(r)}),[null===(t=l.homeStore.selectEntity)||void 0===t?void 0:t.type,l.homeStore.viewMode]);var K=hn((0,z.useState)(!0),2),Y=K[0],Z=K[1],X=function(n){Z(!1),setTimeout((function(){x(n||"null"),Z(!0),"3D"===l.homeStore.viewMode&&Z(!1)}),30)},J=window.innerWidth,Q=(window.innerHeight,hn((0,z.useState)({top:80,left:J/2}),2)),nn=Q[0],en=Q[1],tn=hn((0,z.useState)(!1),2),rn=tn[0],on=tn[1],an=(0,z.useRef)(null),cn=(0,z.useRef)({top:0,left:0}),fn=(0,z.useRef)({x:0,y:0}),mn=hn((0,z.useState)(!1),2),gn=mn[0],vn=mn[1],yn=function(n){on(!0),cn.current={top:nn.top,left:nn.left},fn.current={x:n.touches[0].clientX,y:n.touches[0].clientY}},wn=function(n){n.preventDefault()},jn=function(n){},Sn=function(){return{position:"fixed",top:nn.top,left:"50%",maxWidth:C||gn?P+"px":"550px",maxHeight:C?P+"px":gn?"550px":P+"px",minWidth:P+"px",minHeight:P+"px",flexDirection:gn?"column":"row"}},_n=function(){return(0,r.jsx)("div",{onClick:function(){return f(!d)},style:{borderTopLeftRadius:"50%",width:gn?64:24,height:gn?24:64,backgroundColor:"#fff"}})},An=function(){return C&&(0,r.jsx)(un.A,{type:"icon-a-TypegongjuStateDefault",style:{fontSize:"31px",color:"#282828",margin:"-3px 14px 0px 17px"},onClick:Cn})},Cn=function(){!function(n,e,t){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];en({top:n,left:e}),k(t),vn(r)}(60,J/2,!1,!1)};(0,z.useEffect)((function(){var n=an.current;return n&&n.addEventListener("touchend",jn),function(){n&&n.removeEventListener("touchend",jn)}}),[rn,M]),(0,z.useEffect)((function(){Cn()}),[l.homeStore.IsLandscape]),(0,z.useEffect)((function(){4===l.homeStore.zIndexOf3DViewer&&In()}),[l.homeStore.zIndexOf3DViewer]);var kn=function(){return(n=function(){var n,e;return bn(this,(function(t){switch(t.label){case 0:return t.trys.push([0,3,,4]),[4,fetch("https://3vj-render.3vjia.com/config/3d/aidraw.json")];case 1:return[4,t.sent().json()];case 2:return n=t.sent(),E(n),[3,4];case 3:return e=t.sent(),dn.error("获取配置失败:",e),[3,4];case 4:return[2]}}))},function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){pn(o,r,i,a,l,"next",n)}function l(n){pn(o,r,i,a,l,"throw",n)}a(void 0)}))})();var n},In=function(){switch(h){case"null":return(0,r.jsx)(r.Fragment,{});case"Furniture":case"BaseGroup":case"Door":case"Window":return(0,r.jsxs)("div",{ref:an,onTouchStart:yn,onTouchMove:wn,style:Sn(),className:"".concat(a.root," ").concat(Y?a.show:a.hide),children:[_n(),An(),"                    ",b.map((function(n,e){return(0,r.jsxs)("div",{className:a.btnInfo,onClick:function(){if(n.onClick)n.onClick();else switch(n.id){case"rotate":N.nb.RunCommand(N._I.RotateFurniture);break;case"flip":N.nb.RunCommand(N._I.FlipFurniture);break;case"flip_vertical":N.nb.RunCommand(N._I.FlipFurnitureVertical);break;case"delete":case"deleteRoomSubArea":N.nb.RunCommand(N._I.DeleteFurniture);break;case"deleteRuler":N.nb.RunCommand(N._I.DeleteRuler);break;case"copy":N.nb.RunCommand(N._I.CopyFurniture);break;case"combination":_(!0);break;case"ungroupTemplate":N.nb.DispatchEvent(N.n0.HandleUnGroupTemplate,{}),l.homeStore.setKey(Date.now());break;case"replace":N.nb.emit_M(sn.$.showPopup,n.id),Z(!1);break;case"size":l.homeStore.setSizeInfo({type:"size",visible:!0});break;case"pos_z":l.homeStore.setSizeInfo({type:"pos_z",visible:!0});break;case"copyRoomSubArea":N.nb.DispatchEvent(N.n0.CopyRoomSubArea,null)}},children:[n.divider&&(0,r.jsx)("div",{className:"divider"}),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{children:(0,r.jsx)(un.A,{type:n.icon,style:{fontSize:"20px",color:"#282828"}})}),!d&&(0,r.jsx)("div",{className:"label",children:n.label})]})]},e)})),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{children:(0,r.jsx)(un.A,{type:"icon-a-tianchongFace-1",onClick:function(){N.nb.DispatchEvent(N.n0.cleanSelect,null)},style:{fontSize:"20px",color:"#BCBEC2"}})})}),_n()]});case"RoomArea":return(0,r.jsxs)("div",{ref:an,onTouchStart:yn,onTouchMove:wn,className:"".concat(a.root," ").concat(Y?a.show:a.hide),style:Sn(),children:[An(),_n(),(0,r.jsx)(r.Fragment,{children:G.map((function(n,e){return(0,r.jsxs)("div",{className:a.btnInfo,style:{margin:gn?"4px 0":"0 8px"},onClick:function(){n.onClick?(n.onClick(),w(Math.floor(1e4*Math.random()))):(null==n?void 0:n.EventName)&&(w(Math.floor(1e4*Math.random())),N.nb.DispatchEvent(null==n?void 0:n.EventName,l.homeStore.selectEntity))},children:[(0,r.jsx)("div",{children:(0,r.jsx)(un.A,{type:n.icon,style:{fontSize:"20px",color:"#282828"}})}),!d&&(0,r.jsx)("div",{className:"label",children:n.label})]},e)}))}),_n()]});default:return null}};return(0,r.jsx)("div",{children:In()},y)})),vn=t(7474),yn=t(40561),wn=t(49944);function jn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Sn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return jn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return jn(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var _n=(0,M.observer)((function(n){var e=(0,i.B)().t,t=(0,vn.A)().styles,o=(0,O.P)(),a=Sn((0,z.useState)(0),2),l=a[0],c=a[1],s=Sn((0,z.useState)(0),2),u=s[0],d=s[1],f=Sn((0,z.useState)(0),2),p=f[0],h=f[1],x=Sn((0,z.useState)(0),2),m=x[0],b=x[1],g=Sn((0,z.useState)(null),2),v=g[0],y=g[1],w=Sn((0,z.useState)(!1),2),j=w[0],S=w[1];(0,z.useEffect)((function(){N.nb.instance&&N.nb.on(U.U.AttributeHandle,(function(n){var e,t,r,i,a,l,s,u;"init"===n.mode&&(d(Math.round(null==n||null===(t=n.properties)||void 0===t||null===(e=t.width)||void 0===e?void 0:e.defaultValue)),c(Math.round(null==n||null===(i=n.properties)||void 0===i||null===(r=i.length)||void 0===r?void 0:r.defaultValue)),h(Math.round(null==n||null===(l=n.properties)||void 0===l||null===(a=l.height)||void 0===a?void 0:a.defaultValue)),b(Math.round(null==n||null===(u=n.properties)||void 0===u||null===(s=u.pos_z)||void 0===s?void 0:s.defaultValue)),y(null==n?void 0:n.properties),o.homeStore.setAttribute(n))}))}),[]);var _=(0,z.useCallback)((function(n,e){return function(t){var r;if(n(t),null==v||null===(r=v[e])||void 0===r||r.onChange(t),j){var i,o,a=1,s=1,f=1;if("length"===e)a=t/l,d(Math.round(u*a)),h(Math.round(p*a)),null==v||null===(i=v.width)||void 0===i||i.onChange(u*a),null==v||null===(o=v.height)||void 0===o||o.onChange(p*a);else if("width"===e){var x,m;s=t/u,c(Math.round(l*s)),h(Math.round(p*s)),null==v||null===(x=v.length)||void 0===x||x.onChange(l*s),null==v||null===(m=v.height)||void 0===m||m.onChange(p*s)}else if("height"===e){var b,g;f=t/p,c(Math.round(l*f)),d(Math.round(u*f)),null==v||null===(b=v.length)||void 0===b||b.onChange(l*f),null==v||null===(g=v.width)||void 0===g||g.onChange(u*f)}}}}),[v,j,l,u,p]);return(0,r.jsx)("div",{className:t.container+" leftSizeEditor",onClick:function(n){return n.stopPropagation()},children:"size"===o.homeStore.sizeInfo.type?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:t.title,children:[(0,r.jsxs)("div",{children:[e("尺寸调整"),o.homeStore.IsLandscape&&(0,r.jsxs)("div",{className:t.geometricCheck,children:[(0,r.jsx)("input",{type:"checkbox",onChange:function(n){S(n.target.checked)}}),(0,r.jsxs)("span",{children:[" ",e("等比缩放")]})]})]}),!o.homeStore.IsLandscape&&(0,r.jsx)("button",{className:t.resetBtn,onClick:function(){S(!j)},children:(0,r.jsx)(un.A,{type:"icon-suoding1"})}),(0,r.jsx)("button",{className:t.resetBtn,onClick:function(){N.nb.DispatchEvent(N.n0.ResetSize,null)},children:o.homeStore.IsLandscape?e("恢复默认"):(0,r.jsx)(un.A,{type:"icon-reset"})})]}),o.homeStore.IsLandscape?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:t.sliderContainer,children:[e("宽度"),(0,r.jsx)(yn.A,{className:t.slider,value:l,onChange:_(c,"length"),min:0,max:6e3,step:1}),(0,r.jsx)(wn.A,{className:t.input,value:l,suffix:"mm",onChange:function(n){return _(c,"length")(Number(n.target.value))}})]}),(0,r.jsxs)("div",{className:t.sliderContainer,children:[e("深度"),(0,r.jsx)(yn.A,{className:t.slider,value:u,onChange:_(d,"width"),min:0,max:6e3,step:1}),(0,r.jsx)(wn.A,{className:t.input,value:u,suffix:"mm",onChange:function(n){return _(d,"width")(Number(n.target.value))}})]}),(0,r.jsxs)("div",{className:t.sliderContainer,children:[e("高度"),(0,r.jsx)(yn.A,{className:t.slider,value:p,onChange:_(h,"height"),min:0,max:2800,step:1}),(0,r.jsx)(wn.A,{className:t.input,value:p,suffix:"mm",onChange:function(n){return _(h,"height")(Number(n.target.value))}})]}),(0,r.jsxs)("div",{className:t.sliderContainer,children:[e("投影面积(宽*高)"),(0,r.jsxs)("label",{className:t.input,style:{textAlign:"right"},children:[(l*p/1e3/1e3).toFixed(2),"m²"]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:t.sliderContainer,children:[e("宽度"),(0,r.jsx)(wn.A,{className:t.input,value:l,suffix:"mm",onChange:function(n){return _(c,"length")(Number(n.target.value))}})]}),(0,r.jsx)(yn.A,{className:t.slider,value:l,onChange:_(c,"length"),min:0,max:6e3,step:1}),(0,r.jsxs)("div",{className:t.sliderContainer,children:[e("深度"),(0,r.jsx)(wn.A,{className:t.input,value:u,suffix:"mm",onChange:function(n){return _(d,"width")(Number(n.target.value))}})]}),(0,r.jsx)(yn.A,{className:t.slider,value:u,onChange:_(d,"width"),min:0,max:6e3,step:1}),(0,r.jsxs)("div",{className:t.sliderContainer,children:[e("高度"),(0,r.jsx)(wn.A,{className:t.input,value:p,suffix:"mm",onChange:function(n){return _(h,"height")(Number(n.target.value))}})]}),(0,r.jsx)(yn.A,{className:t.slider,value:p,onChange:_(h,"height"),min:0,max:2800,step:1}),(0,r.jsxs)("div",{className:t.sliderContainer,children:[e("投影面积(宽*高)"),(0,r.jsxs)("label",{className:t.input,children:[(l*p/1e3/1e3).toFixed(2),"m²"]})]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:t.title,children:[(0,r.jsx)("div",{children:e("离地")}),(0,r.jsx)("button",{className:t.resetBtn,onClick:function(){N.nb.DispatchEvent(N.n0.ResetSize,null)},children:o.homeStore.IsLandscape?e("恢复默认"):(0,r.jsx)(un.A,{type:"icon-reset"})})]}),(0,r.jsxs)("div",{className:t.sliderContainer,children:[e("离地"),o.homeStore.IsLandscape&&(0,r.jsx)(yn.A,{className:t.slider,value:m,onChange:_(b,"pos_z"),min:0,max:2800,step:1}),(0,r.jsx)(wn.A,{className:t.input,value:m,suffix:"mm",onChange:function(n){return _(b,"pos_z")(Number(n.target.value))}})]}),!o.homeStore.IsLandscape&&(0,r.jsx)(yn.A,{className:t.slider,value:m,onChange:_(b,"pos_z"),min:0,max:2800,step:1})]})})})),An=t(21491),Cn=t(59525),kn=t(17365),In=t(10371),Mn=t(14368),zn=t(86906);function Nn(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Dn(){var n=Nn(["\n            width: 100%;\n            background-color: #fff;\n            border-radius: 4px;\n            padding: 0;\n            position: relative;\n        "]);return Dn=function(){return n},n}function En(){var n=Nn(["\n            text-align: center;\n            font-size: 16px;\n            font-weight: 500;\n            color: #333;\n            padding: 12px 0;\n            border-bottom: 1px solid #f0f0f0;\n        "]);return En=function(){return n},n}function Pn(){var n=Nn(["\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            margin-bottom: 16px;\n            padding-bottom: 8px;\n            border-bottom: 1px solid #f0f0f0;\n        "]);return Pn=function(){return n},n}function Fn(){var n=Nn(["\n            font-size: 16px;\n            font-weight: 500;\n            color: #333;\n        "]);return Fn=function(){return n},n}function Bn(){var n=Nn(["\n            cursor: pointer;\n            color: #999;\n            font-size: 16px;\n\n            &:hover {\n                color: #666;\n            }\n        "]);return Bn=function(){return n},n}function On(){var n=Nn(["\n            margin-bottom: 16px;\n            padding: 0 16px;\n            &:first-of-type {\n                margin-top: 16px;\n            }\n        "]);return On=function(){return n},n}function Rn(){var n=Nn(["\n            display: block;\n            margin-bottom: 8px;\n            font-size: 14px;\n            color: #333;\n        "]);return Rn=function(){return n},n}function Tn(){var n=Nn(["\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            height: 36px;\n            padding: 0;\n            \n            span {\n                margin-bottom: 0;\n                flex: 1;\n            }\n        "]);return Tn=function(){return n},n}function Ln(){var n=Nn(["\n            width: 100%;\n            height: 36px;\n            border: 1px solid #e8e8e8;\n            border-radius: 4px;\n            background-color: #fff;\n            position: relative;\n\n            &.ant-select-focused .ant-select-selector,\n            .ant-select-selector:hover,\n            .ant-select-selector:focus {\n                border-color: #40a9ff !important;\n                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;\n            }\n\n            .ant-select-selector {\n                border: none !important;\n                box-shadow: none !important;\n                height: 100% !important;\n                padding: 0 11px !important;\n            }\n        "]);return Ln=function(){return n},n}function Un(){var n=Nn(["\n            width: 100%;\n            height: 36px;\n            border: 1px solid #e8e8e8;\n            border-radius: 4px;\n            padding: 0 12px;\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n        "]);return Un=function(){return n},n}function Wn(){var n=Nn(["\n            width: 100%;\n            padding: 8px 0;\n\n            .ant-slider-track {\n                background-color: #1890ff;\n            }\n        "]);return Wn=function(){return n},n}function Hn(){var n=Nn(["\n            display: flex;\n            align-items: center;\n            justify-content: flex-end;\n            border: 1px solid #e8e8e8;\n            border-radius: 4px;\n            height: 36px;\n            padding: 0 12px;\n        "]);return Hn=function(){return n},n}function Vn(){var n=Nn(["\n            width: 36px;\n            height: 24px;\n            border-radius: 2px;\n            background-color: #ffa500;\n            cursor: pointer;\n            border: 1px solid #e8e8e8;\n            \n            &:hover {\n                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n            }\n        "]);return Vn=function(){return n},n}function $n(){var n=Nn(["\n            margin-top: 0;\n            margin-bottom: 16px;\n            padding: 0 16px;\n        "]);return $n=function(){return n},n}function Gn(){var n=Nn(["\n            width: 100%;\n            height: 36px;\n            background-color: #f5f5f5;\n            border: none;\n            border-radius: 4px;\n            color: #333;\n            cursor: pointer;\n\n            &:hover {\n                background-color: #e8e8e8;\n            }\n        "]);return Gn=function(){return n},n}function qn(){var n=Nn(["\n            color: #999;\n            margin-left: 4px;\n        "]);return qn=function(){return n},n}var Kn=(0,o.rU)((function(n){var e=n.css;return{container:e(Dn()),titleBar:e(En()),header:e(Pn()),title:e(Fn()),closeIcon:e(Bn()),formItem:e(On()),label:e(Rn()),colorRow:e(Tn()),select:e(Ln()),inputNumber:e(Un()),slider:e(Wn()),colorPicker:e(Hn()),colorBlock:e(Vn()),footer:e($n()),footerButton:e(Gn()),unitText:e(qn())}})),Yn=t(62867),Zn=t(13719);function Xn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Jn(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Xn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Xn(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Qn=function(n){!function(n){if(null==n)throw new TypeError("Cannot destructure "+n)}(n);var e=Kn().styles,t=(0,O.P)(),i=Jn((0,z.useState)("客厅区"),2),o=i[0],a=i[1],l=Jn((0,z.useState)("#FFA500"),2),c=l[0],s=l[1],u=Jn((0,z.useState)([]),2),d=u[0],f=u[1],p=Jn((0,z.useState)(!1),2),h=p[0],x=p[1],m=Jn((0,z.useState)(null),2),b=m[0],g=m[1];(0,z.useEffect)((function(){var n=Yn.a.getRoomSubAreaMenuData().map((function(n){return{value:n.id,label:n.text}}));f(n),N.nb.on_M(Zn.$.showPopup,"SpaceAreaAttribute",(function(e){var r;if("SpaceAreaAttribute"!==e&&h)x(!1);else if((null===(r=t.homeStore.selectEntity)||void 0===r?void 0:r.type)===W.Fz.RoomSubArea){x(!0);var i=t.homeStore.selectEntity;g(i);var o=i.space_area_type,l=n.find((function(n){return n.value===o}));a(l),s(i.color_style)}else x(!1)}))}),[h]);return h&&(0,r.jsxs)("div",{className:e.container,children:[(0,r.jsxs)("div",{className:e.formItem,children:[(0,r.jsx)("div",{className:e.label,children:"分区类型"}),(0,r.jsx)(Mn.A,{className:e.select,value:o,onChange:function(n){a(n),b&&n in W.fZ&&N.nb.DispatchEvent(N.n0.UpdateRoomSubAreaType,n)},options:d})]}),(0,r.jsx)("div",{className:e.formItem,children:(0,r.jsxs)("div",{className:e.colorRow,children:[(0,r.jsx)("span",{className:e.label,children:"颜色"}),(0,r.jsx)(zn.A,{value:c,onChangeComplete:function(n){s(n.toHexString()),b&&(Yn.a.updateSubAreaColor(b,n.toHexString()),N.nb.instance.update())}})]})}),(0,r.jsx)("div",{className:e.footer,children:(0,r.jsx)("button",{className:e.footerButton,onClick:function(){b&&N.nb.RunCommand(N._I.DeleteFurniture)},children:"删除分区"})})]})};function ne(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function ee(){var n=ne(["\n      position: absolute;\n      top: 0;\n      left: 0;\n      z-index: 8;\n      width: 100%;\n      height: 100%;\n      border-width: 40px 45px;\n      border-color: #000000;\n      border-style: solid;\n      opacity: 0.35;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      box-sizing: border-box;\n      pointer-events: none;\n    "]);return ee=function(){return n},n}function te(){var n=ne(["\n      width: 100%;\n      height: 100%;\n      background-color: transparent; /* 中间区域透明 */\n      border: 3px solid #ffffff; /* 添加白色边框 */\n      border-radius: 3px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      pointer-events: none;\n    "]);return te=function(){return n},n}var re=(0,o.rU)((function(n){var e=n.css;return{box:e(ee()),contentBox:e(te())}}));function ie(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function oe(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return ie(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ie(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var ae=.83,le=(0,M.observer)((function(){var n=re().styles,e=(0,O.P)(),t=(0,z.useRef)(null),i=oe((0,z.useState)(window.innerWidth<window.innerHeight),2),o=i[0],a=i[1],l=oe((0,z.useState)({borderWidth:"0px",borderHeight:"0px",contentWidth:"100%",contentHeight:"100%"}),2),c=l[0],s=l[1];(0,z.useEffect)((function(){var n=function(){var n=window.innerWidth<window.innerHeight;a(n),u(e.homeStore.aspectRatioMode,n)};return window.addEventListener("resize",n),function(){return window.removeEventListener("resize",n)}}),[]),(0,z.useEffect)((function(){u(e.homeStore.aspectRatioMode,o)}),[e.homeStore.aspectRatioMode,o]);var u=function(n){var e,t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o,i=window.innerWidth,a=window.innerHeight,l=[{width:4,height:3},{width:16,height:9},{width:3,height:4},{width:9,height:16},{width:0,height:0}][n-1];if(5==n)return e=i,t=a,void s({borderWidth:"".concat(0,"px"),borderHeight:"".concat(0,"px"),contentWidth:"".concat(e,"px"),contentHeight:"".concat(t,"px")});r?(t=(e=i*ae)*l.height/l.width)>a*ae&&(e=(t=a*ae)*l.width/l.height):(e=(t=a*ae)*l.width/l.height)>i*ae&&(t=(e=i*ae)*l.height/l.width),s({borderWidth:"".concat((i-e)/2,"px"),borderHeight:"".concat((a-t)/2,"px"),contentWidth:"".concat(e,"px"),contentHeight:"".concat(t,"px")})};return(0,z.useEffect)((function(){if(t.current){var e=t.current;e.style.borderLeftWidth=c.borderWidth,e.style.borderRightWidth=c.borderWidth,e.style.borderTopWidth=c.borderHeight,e.style.borderBottomWidth=c.borderHeight;var r=e.querySelector(".".concat(n.contentBox));r&&(r.style.width=c.contentWidth,r.style.height=c.contentHeight)}}),[c,n.contentBox]),(0,r.jsx)("div",{className:n.box,ref:t,children:(0,r.jsx)("div",{className:n.contentBox})})})),ce=t(43417),se=t(79750);function ue(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function de(){var n=ue(["\n      font-weight: 600;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 20px;\n      font-size: 16px;\n      color: rgba(255, 255, 255, 0.60);\n    "]);return de=function(){return n},n}function fe(){var n=ue(["\n      position: fixed;\n      left: 50%;\n      transform: translate(-50%, 0);\n      z-index: 999;\n      bottom: 15px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      width: auto;\n      color: #fff;\n    "]);return fe=function(){return n},n}function pe(){var n=ue(["\n      z-index:999;\n      margin-right: 10px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      background: rgba(0, 0, 0, 0.40);\n      backdrop-filter: blur(50px);\n      border-radius: 20px;\n      cursor: pointer;\n      .selectInfo {\n        z-index: 1;\n        position: absolute;\n        left: 50%;\n        bottom: 50px;\n        transform: translate(-50%);\n        max-width: 500px;\n        overflow-x: scroll;\n        transition: height 0.3;\n        display: flex;\n        scroll-behavior: smooth;\n        ::-webkit-scrollbar\n        {\n          display: none;\n        }\n        .shijiaoItem\n        {\n          width: 100%;\n          font-size: 16px;\n          text-align: center;\n          position: relative;\n          margin-right: 8px;\n          transition: all .3s;\n          border-radius: 8px;\n          width: 127px;\n          height: 95px;\n          img{\n            width: 122px;\n            height: 100%;\n            border-radius: 8px;\n          }\n          .title\n          {\n            position: absolute;\n            bottom: 0px;\n            left: 50%;\n            transform: translate(-50%);\n            color: #fff;\n            font-size: 14px;\n            background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 1.43%, rgba(0, 0, 0, 0.60) 101.43%);\n            width: 123px;\n            height: 35px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            border-radius: 8px;\n          }\n        }\n        .shijiaoItem:hover\n        {\n          background-color: #ffffff1a;\n          transition: all 0.3;\n        }\n      }\n    "]);return pe=function(){return n},n}function he(){var n=ue(["\n      border-right: 1px solid rgba(255,255,255,.1);\n      height: 42px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      width: 45px;\n    "]);return he=function(){return n},n}function xe(){var n=ue(["\n      border-left: 1px solid rgba(255,255,255,.1);\n      height: 42px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      width: 45px;\n    "]);return xe=function(){return n},n}function me(){var n=ue(["\n      width: 80px;\n      height: 42px;\n      font-size: 16px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 100px;\n      }\n    "]);return me=function(){return n},n}function be(){var n=ue(["\n      z-index:999;\n      background: #2323234d;\n      border-radius: 20px;\n      cursor: pointer;\n      color: #eee;\n      font-size: 16px;\n      width: 120px;\n      height: 42px;\n      display: flex;\n      justify-content: space-between;\n      padding: 0 20px;\n      align-items: center;\n      background: linear-gradient(90deg,#d07bff,#7a5bff);\n      .submit\n      {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        width: 100%;\n      }\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 120px;\n        left: 75%;\n      }\n    "]);return be=function(){return n},n}function ge(){var n=ue(["\n      font-size: 14px;\n      color: rgba(255, 255, 255, 0.80);\n      font-weight: 600;\n      margin-bottom: 15px; \n    "]);return ge=function(){return n},n}function ve(){var n=ue(["\n    "]);return ve=function(){return n},n}function ye(){var n=ue(["\n      z-index: 99;\n      position: fixed;\n      left: 12px;\n      top: 52px;\n      width: 200px;\n      height: 90%;\n      background: rgba(0, 0, 0, 0.40);\n      backdrop-filter: blur(50px);\n      border-radius: 8px;\n      overflow: hidden;\n      transition: height 0.3;\n      padding: 16px;\n      overflow-x: hidden;\n      .ant-tag-checkable-checked \n      {\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      }\n      .ant-tag-checkable-checked:hover\n      {\n        background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      }\n      .ant-slider-track \n      {\n        background: #9156FF !important;\n      }\n      .ant-slider .ant-slider-dot-active\n      {\n        border-color: #9156FF !important;\n      }\n      .ant-tag:not(.ant-tag-checkable-checked)\n      {\n        background: rgba(0, 0, 0, 0.40);\n        color: #fff;\n      }\n      .ant-tag{\n        width: 76px;\n        text-align: center;\n        margin-bottom: 8px;\n      }\n      .ant-slider-mark-text\n      {\n        color: #ffffffbf;\n        font-size: 10px;\n      }\n    "]);return ye=function(){return n},n}var we=(0,o.rU)((function(n){var e=n.css;return{titleTag:e(de()),submitContainer:e(fe()),shijiaoBarContainer:e(pe()),leftArrow:e(he()),rightArrow:e(xe()),shijiaoBar:e(me()),submitBtn:e(be()),label:e(ge()),lensContainer:e(ve()),submitInfo:e(ye())}})),je=t(42269),Se=t(87248),_e=t(38775),Ae=t(61535),Ce=t(27164),ke=t(93413);function Ie(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Me(){var n=Ie(["\n      z-index:999;\n      margin-right: 10px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      background: rgba(0, 0, 0, 0.40);\n      backdrop-filter: blur(50px);\n      border-radius: 20px;\n      cursor: pointer;\n      .selectInfo {\n        z-index: 1;\n        position: absolute;\n        left: 50%;\n        bottom: 50px;\n        transform: translate(-50%);\n        max-width: 500px;\n        overflow-x: scroll;\n        transition: height 0.3;\n        display: flex;\n        scroll-behavior: smooth;\n        ::-webkit-scrollbar\n        {\n          display: none;\n        }\n        .shijiaoItem\n        {\n          width: 100%;\n          font-size: 16px;\n          text-align: center;\n          position: relative;\n          margin-right: 8px;\n          transition: all .3s;\n          border-radius: 8px;\n          width: 127px;\n          height: 95px;\n          img{\n            width: 122px;\n            height: 100%;\n            border-radius: 8px;\n          }\n          .title\n          {\n            position: absolute;\n            bottom: 0px;\n            left: 50%;\n            transform: translate(-50%);\n            color: #fff;\n            font-size: 14px;\n            background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 1.43%, rgba(0, 0, 0, 0.60) 101.43%);\n            width: 123px;\n            height: 35px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            border-radius: 8px;\n          }\n        }\n        .shijiaoItem:hover\n        {\n          background-color: #ffffff1a;\n          transition: all 0.3;\n        }\n      }\n    "]);return Me=function(){return n},n}function ze(){var n=Ie(["\n      border-right: 1px solid rgba(255,255,255,.1);\n      height: 42px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      width: 45px;\n    "]);return ze=function(){return n},n}function Ne(){var n=Ie(["\n      border-left: 1px solid rgba(255,255,255,.1);\n      height: 42px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      width: 45px;\n    "]);return Ne=function(){return n},n}function De(){var n=Ie(["\n      width: 80px;\n      height: 42px;\n      font-size: 16px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      color: #fff;\n      @media screen and (max-width: 450px) { // 手机宽度\n        width: 100px;\n      }\n    "]);return De=function(){return n},n}var Ee=(0,o.rU)((function(n){var e=n.css;return{shijiaoBarContainer:e(Me()),leftArrow:e(ze()),rightArrow:e(Ne()),shijiaoBar:e(De())}}));function Pe(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Fe(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Pe(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Pe(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Be=(0,M.observer)((function(){(0,i.B)().t;var n=Ee().styles,e=((0,q.Zp)(),(0,O.P)()),t=Fe((0,z.useState)(0),2),o=t[0],a=t[1],l=Fe((0,z.useState)(!1),2),c=l[0],s=l[1];return(0,z.useEffect)((function(){var n=N.nb.instance.scene3D;n.active_controls.bindViewEntity(e.homeStore.currentViewCameras[o]),n.update()}),[o]),(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:n.shijiaoBarContainer,children:[(0,r.jsx)("div",{onClick:function(){a(o>0?o-1:e.homeStore.currentViewCameras.length-1)},className:n.leftArrow,children:(0,r.jsx)(Ce.A,{style:{color:"#bcb9b9",fontSize:14},iconClass:"iconfill_left"})}),(0,r.jsxs)("div",{className:n.shijiaoBar,onClick:function(){s(!c)},children:["视角",o+1]}),(0,r.jsx)("div",{onClick:function(){o<e.homeStore.currentViewCameras.length-1?a(o+1):a(0)},className:n.rightArrow,children:(0,r.jsx)(Ce.A,{style:{color:"#bcb9b9",fontSize:14},iconClass:"iconfill_right"})}),(0,r.jsx)("div",{className:"selectInfo",onWheel:function(n){n.preventDefault();n.currentTarget.scrollLeft+=30*n.deltaY},style:{height:c?"auto":0},children:e.homeStore.currentViewCameras.map((function(n,e){return(0,r.jsxs)("div",{className:"shijiaoItem",style:{border:e==o?"2px solid #147FFA":"2px solid #fff"},onClick:function(){N.nb.instance.scene3D.active_controls.bindViewEntity(n),a(e)},children:[(0,r.jsx)("img",{src:n._perspective_img.src,alt:""}),(0,r.jsxs)("div",{className:"title",children:["视角",e+1]})]},e)}))})]})})})),Oe=t(65640);function Re(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Te(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Le(n){return function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){Te(o,r,i,a,l,"next",n)}function l(n){Te(o,r,i,a,l,"throw",n)}a(void 0)}))}}function Ue(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return Re(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Re(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function We(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var He=void 0,Ve=(0,M.observer)((function(){var n=we().styles,e=(0,O.P)(),t=e.homeStore,i=t.drawPictureMode,o=t.aspectRatioMode,a=t.setAspectRatioMode,l=je.A.CheckableTag,c=((0,z.useRef)(null),Ue((0,z.useState)(1),2)),s=c[0],u=c[1],d=Ue((0,z.useState)(300),2),f=d[0],p=d[1],h=(e.homeStore.guideMapCurrentRoom,Ue((0,z.useState)(20),2));h[0],h[1];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:n.submitContainer,children:[(0,r.jsx)(Be,{}),(0,r.jsx)("div",{className:n.submitBtn,children:(0,r.jsx)("div",{className:"submit",onClick:function(){return Le((function(){return We(this,(function(n){switch(n.label){case 0:return"aiDrawing"!==i?[3,1]:(N.nb.emit_M(Z.r.AiDrawingCapture,!0),[3,3]);case 1:return"render"!==i?[3,3]:(Oe.log("渲染 标清出图"),Se.A.loading("提交渲染中...",0),N.nb.instance.renderSubmitObject={drawPictureMode:i,radioMode:o,resolution:s},[4,(e="SD",Le((function(){var n;return We(this,(function(t){switch(t.label){case 0:return Ae.j.resolutionTag=e,[4,Ae.j.commitOfflineRender()];case 1:return(n=t.sent()).success?(N.nb.instance.layout_container._layout_scheme_id,n.queueId,Se.A.destroy(),Se.A.success("提交渲染成功！")):(Se.A.destroy(),Se.A.error(n.msg)),[2]}}))}))())]);case 2:n.sent(),n.label=3;case 3:return[2]}var e}))}))()},children:"提交渲染"})})]}),(0,r.jsx)(ke.A,{in:e.homeStore.showSubmitInfo,timeout:300,classNames:{enter:"fadeEnter",enterActive:"fadeEnterActive",exit:"fadeExit",exitActive:"fadeExitActive"},unmountOnExit:!0,children:(0,r.jsxs)("div",{className:n.submitInfo,children:[(0,r.jsxs)("div",{className:n.titleTag,children:[(0,r.jsx)("div",{children:"aiDrawing"==i?"AI绘图":"标准渲染"}),(0,r.jsx)(Ce.A,{onClick:function(){e.homeStore.setShowSubmitInfo(!1)},style:{fontSize:14,cursor:"pointer"},iconClass:"icon-icon"})]}),(0,r.jsx)("div",{className:n.label,children:"构图"}),(0,r.jsx)("div",{style:{marginBottom:20},children:[{lable:"4:3",radioMode:1},{lable:"16:9",radioMode:2},{lable:"3:4",radioMode:3},{lable:"9:16",radioMode:4},{lable:"原图",radioMode:5}].map((function(n){return(0,r.jsx)(l,{checked:o==n.radioMode,onChange:function(e){a(n.radioMode)},children:n.lable},n.radioMode)}))}),(0,r.jsx)("div",{className:n.label,children:"视角"}),(0,r.jsxs)("div",{className:n.lensContainer,children:[(0,r.jsx)("div",{style:{marginTop:12,marginRight:20,color:"rgba(255, 255, 255, 0.80)"},children:"镜头"}),(0,r.jsx)("div",{children:(0,r.jsx)(yn.A,{style:{width:"160px"},marks:{20:{label:"特写"},40:{label:"人眼"},65:{label:"标准"},90:{label:"广角"}},defaultValue:65,step:1,min:20,max:90,onChange:function(n){var e=N.nb.instance.scene3D.active_controls.camera;e.fov=n,e.updateProjectionMatrix(),N.nb.emit_M(U.U.Scene3DCameraChanged,He)}})})]}),(0,r.jsxs)("div",{className:n.lensContainer,children:[(0,r.jsx)("div",{style:{marginTop:12,marginRight:20,color:"rgba(255, 255, 255, 0.80)"},children:"裁剪"}),(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:20},children:[(0,r.jsx)(yn.A,{style:{width:"120px"},min:300,max:2500,step:10,defaultValue:300,onChange:function(n){var e=N.nb.instance.scene3D.active_controls.camera;e.near=n,e.updateProjectionMatrix(),N.nb.emit_M(U.U.Scene3DCameraChanged,He),p(n)}}),(0,r.jsx)(_e.A,{min:300,max:2500,step:10,value:f,style:{width:"70px"},onChange:function(n){var e=N.nb.instance.scene3D.active_controls.camera;e.near=n,e.updateProjectionMatrix(),N.nb.emit_M(U.U.Scene3DCameraChanged,He),p(n)}})]})]}),"render"==i&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:n.label,children:"分辨率"}),(0,r.jsx)("div",{style:{marginBottom:20},children:[{lable:"标清",radioMode:1},{lable:"高清",radioMode:2}].map((function(n){return(0,r.jsx)(l,{checked:s==n.radioMode,onChange:function(e){1===n.radioMode?u(n.radioMode):Se.A.warning("会员权益，暂未开放！")},children:n.lable},n.radioMode)}))})]})]})})]})}));function $e(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function Ge(){var n=$e(["\n    position: fixed;\n    top: 5px;\n    left: 50%;\n    transform: translateX(-50%);\n    max-width: 200px;\n    width: auto;\n    height: 32px;\n    display: flex;\n    align-items: center;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n    z-index: 999;\n    border-radius: 8px;\n    background: rgba(0, 0, 0, 0.40);\n    backdrop-filter: blur(50px);;\n    justify-content: space-between;\n    @media screen and (max-width: 450px) { // 手机宽度\n      min-width: 250px;\n      height: 36px;\n    }\n  "]);return Ge=function(){return n},n}function qe(){var n=$e(["\n    color: #333;\n    font-size: 14px;\n    margin-right: 12px;\n    max-width: 40%;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  "]);return qe=function(){return n},n}function Ke(){var n=$e(["\n    background-color: #1890ff;\n    color: white;\n    border: none;\n    padding: 6px 20px;\n    height: 28px;\n    line-height: 16px;\n    border-radius: 2px;\n    cursor: pointer;\n    font-size: 14px;\n    margin-left: 12px;\n    &:hover {\n      background-color: #40a9ff;\n    }\n  "]);return Ke=function(){return n},n}function Ye(){var n=$e(["\n    color: #999;\n    font-size: 14px;\n    margin-left: 12px;\n    white-space: nowrap;\n  "]);return Ye=function(){return n},n}function Ze(){var n=$e(["\n    width: 100px;\n    color: #d6d1d1;\n    font-size: 14px;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n    position: relative;\n    @media screen and (max-width: 450px) { // 手机宽度\n      width: 60px;\n      font-size: 12px;\n    }\n    @media screen and (max-width: 350px) { // 手机宽度\n    }\n    z-index: 9;\n  "]);return Ze=function(){return n},n}function Xe(){var n=$e(["\n    color: #fff;\n    background: rgba(255, 255, 255, 0.20);\n    backdrop-filter: blur(50px);\n    border-radius: 8px;\n  "]);return Xe=function(){return n},n}var Je=(0,o.rU)((function(n){var e=n.css;return{exitBarContainer:e(Ge()),currentMode:e(qe()),exitButton:e(Ke()),exitHint:e(Ye()),topTabs:e(Ze()),active:e(Xe())}})),Qe=t(57189),nt=t(65640);function et(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function tt(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return et(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return et(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var rt=(0,M.observer)((function(){var n=(0,O.P)(),e=(0,i.B)().t,t=n.homeStore,o=t.drawPictureMode,a=t.setDrawPictureMode,l=(t.setIsdrawPicture,Je().styles),c=tt((0,z.useState)(!1),2),s=(c[0],c[1],[{value:"aiDrawing",label:e("AI绘图")},{label:e("标准渲染"),value:"render"}]);return(0,z.useEffect)((function(){var n=N.nb.instance.scene3D;"render"===o&&(n.setLightMode(In.Ei.Night),Qe.p.instance.updateLighting(!0),N.nb.instance.scene3D.setLightGroupVisible(!1,!1,!1)),"aiDrawing"===o&&(n.setLightMode(In.Ei.Day),Qe.p.instance.cleanLighting(),N.nb.instance.scene3D.setLightGroupVisible(!1,!1,!1),ce.Y.cleanLight())}),[o]),(0,r.jsx)("div",{className:l.exitBarContainer,children:s.map((function(n,e){return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{onClick:function(){return e=n.value,a(e),void nt.log("drawingPicMode",e);var e},className:l.topTabs+(o===n.value?" ".concat(l.active):""),children:[n.label,o===n.value&&(0,r.jsx)("span",{style:{content:'""',position:"absolute",bottom:"2px",left:"38px",width:"20px",height:"2px",backgroundColor:"#fff"}})]},e)})}))})}));function it(n,e){return e||(e=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(e)}}))}function ot(){var n=it(["\n      height: 500px;\n      padding: 12px 12px;\n      border-radius: 12px;\n      @media screen and (orientation: landscape) {\n        height: calc(var(--vh, 1vh) * 100);\n        width: 100%;\n        padding: 12px 12px;\n      }\n      .ant-segmented\n      {\n        background-color: #EAEBEA;\n        color: #282828 !important;\n      }\n    "]);return ot=function(){return n},n}function at(){var n=it(["\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      font-weight: 600;\n      color: rgba(255, 255, 255, 0.60);\n      font-size: 16px;\n      font-style: normal;\n      font-weight: 600;\n      line-height: 24px;\n    "]);return at=function(){return n},n}function lt(){var n=it(["\n        display: flex;\n        justify-content: space-between;\n        margin-top: 20px;\n        padding: 0 24px;\n        @media screen and (orientation: landscape) {\n          margin-top: 12px;\n          padding: 0 0px;\n        }\n        .info\n        {\n          display: flex;\n          img{\n            width: 72px;\n            height: 72px;\n            border-radius: 4px;\n            margin-right: 16px;\n            border-radius: 4px;\n            border: 1px solid #EEE;\n            background: #C3C4C7;\n            @media screen and (orientation: landscape) {\n              width: 48px;\n              height: 48px;\n              margin-right: 12px;\n            }\n          }\n        }\n         .sizeInfo\n         {\n          padding: 8px 0px;\n          color: rgba(255, 255, 255, 0.85);\n          @media screen and (orientation: landscape) {\n            padding: 0px 0px;\n          }\n            .size\n            {\n              color: rgba(255, 255, 255, 0.60);\n              margin-top: 4px;\n              user-select: text;\n              @media screen and (orientation: landscape) {\n                margin-top: 4px;\n                font-size: 10px;\n              }\n            }\n         } \n      "]);return lt=function(){return n},n}function ct(){var n=it(["\n      margin: 0px 0 14px 0px;\n      font-size: 14px;\n      font-weight: 600;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      @media screen and (orientation: landscape) {\n        margin: 12px 0 8px 0px;\n      }\n    "]);return ct=function(){return n},n}function st(){var n=it(["\n      display: flex;\n    "]);return st=function(){return n},n}function ut(){var n=it(["\n      border-radius: 4px;\n      background: rgba(0, 0, 0, 0.40);\n      height: 24px;\n      padding: 2px 8px;\n      display: flex;\n      width: 70px;\n      align-items: center;\n      justify-content: center;\n      gap: 10px;\n      font-size: 12px;\n      margin-right: 8px;\n      color: rgba(255, 255, 255, 0.85);\n    "]);return ut=function(){return n},n}function dt(){var n=it(["\n      background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);\n      color: #fff;\n    "]);return dt=function(){return n},n}function ft(){var n=it(["\n      display: flex;\n      flex-wrap: wrap;\n      overflow-y: scroll;\n      max-height: calc(var(--vh, 1vh) * 100 - 240px);\n      margin-top: 10px;\n      align-items: flex-start;\n       /* 隐藏滚动条 */\n      &::-webkit-scrollbar {\n          display: none; /* 隐藏滚动条 */\n      }\n      \n      /* 对于 Firefox */\n      scrollbar-width: none; /* 隐藏滚动条 */\n      -ms-overflow-style: none; /* IE 和 Edge */\n      @media screen and (orientation: portrait) {\n        overflow-x: scroll;\n        flex-wrap: nowrap;\n        width: 100%; \n      }\n    "]);return ft=function(){return n},n}function pt(){var n=it(["\n      text-align: center;\n      padding: 20px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      margin: 0 auto;\n    "]);return pt=function(){return n},n}function ht(){var n=it(["\n      width: 104px;\n      margin: 6px 12px 0 12px;\n      text-align: center;\n      img{\n        width: 100%;\n        aspect-ratio: 1 / 1;\n        border-radius: 4px;\n        background-color: #eaeaea;\n        border-radius: 8px;\n      }\n      @media screen and (max-width: 800px){\n         width: 112px;\n         img{\n          width: 112px;\n         }\n      }\n      @media screen and (max-width: 450px){\n         width: 106px;\n      }\n      @media screen and (max-width: 400px){\n         width: 94px;\n      }\n      @media screen and (orientation: landscape) {\n        margin: 6px 6px 0 6px;\n        width: 88px;\n        font-size: 10px;\n        text-align: left;\n      }\n\n    "]);return ht=function(){return n},n}function xt(){var n=it(["\n    \n    "]);return xt=function(){return n},n}function mt(){var n=it(["\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      margin-top: 4px;\n      color: rgba(255, 255, 255, 0.85);\n    "]);return mt=function(){return n},n}function bt(){var n=it(["\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      margin: 0 auto;\n      margin-top: 100%;\n      transform: translateY(-50%);\n      .emptyImg{\n        width: 60px;\n        height: 60px;\n        margin-bottom: 12px;\n      }\n      span{\n        color: #fff;\n      }\n    "]);return bt=function(){return n},n}var gt=(0,o.rU)((function(n){var e=n.css;return{root:e(ot()),title:e(at()),topInfo:e(lt()),divider:e(ct()),tabContainer:e(st()),tabItem:e(ut()),active:e(dt()),goodsInfo:e(ft()),loading:e(pt()),goodsItem:e(ht()),selectIcon:e(xt()),sizeInfo:e(mt()),noData:e(bt())}})),vt=t(42322),yt=t(52898),wt=t(31033),jt=t(63038),St=t(44186);function _t(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function At(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Ct(n){return function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){At(o,r,i,a,l,"next",n)}function l(n){At(o,r,i,a,l,"throw",n)}a(void 0)}))}}function kt(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function It(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||function(n,e){if(!n)return;if("string"==typeof n)return _t(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return _t(n,e)}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Mt(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var zt=(0,M.observer)((function(n){var e=n.selectedFigureElement,o=(0,O.P)(),a=(0,i.B)().t,l=gt().styles,c=It((0,z.useState)(null==e?void 0:e._candidate_materials),2),s=c[0],u=c[1],d=It((0,z.useState)("套系素材"),2),f=d[0],p=d[1],h=It((0,z.useState)(!1),2),x=h[0],m=h[1],b=(0,z.useRef)(null);(0,z.useEffect)((function(){e&&u(null==e?void 0:e._candidate_materials),p("套系素材")}),[e]);return(0,z.useEffect)((function(){!function(n){Ct((function(){var t,r,i;return Mt(this,(function(o){switch(o.label){case 0:return"套系素材"!==n?[3,1]:((null==e?void 0:e._candidate_materials)&&(null==e?void 0:e._candidate_materials.length)>0?u(null==e?void 0:e._candidate_materials):u([]),[3,3]);case 1:return m(!0),[4,(0,wt.t5)({categoryId:"",current:1,designMaterialId:null==e||null===(t=e._matched_material)||void 0===t?void 0:t.modelId,size:50,tagIds:[]})];case 2:(r=o.sent()).success&&r.data?(i=r.data.materials.records.map((function(n){return{imageUrl:L.L4+n.imagePath+"?x-oss-process=image/resize,m_fixed,w_120,h_120",name:n.materialName,materialId:n.materialId}})),u(i)):u([]),m(!1),o.label=3;case 3:return[2]}}))}))()}(f)}),[f]),(0,r.jsx)("div",{className:l.root,children:e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:l.title,children:[(0,r.jsx)("div",{children:a("素材替换")}),(0,r.jsx)(un.A,{style:{fontSize:20,color:"#5B5E60"},type:"icon-close1",onClick:function(){N.nb.emit_M(U.U.FigureElementSelected,null)}})]}),e&&(0,r.jsx)("div",{className:l.topInfo,children:(0,r.jsxs)("div",{className:"info",children:[(0,r.jsx)("div",{children:(0,r.jsx)("img",{src:e._matched_material.imageUrl||e.image_path,alt:""})}),(0,r.jsxs)("div",{className:"sizeInfo",children:[(0,r.jsx)("div",{children:e._matched_material.name}),(0,r.jsxs)("div",{className:"size",children:[a("图元尺寸"),"：",Math.round(e.rect._w),"*",Math.round(e.rect._h)]}),(0,r.jsxs)("div",{className:"size",children:[a("模型尺寸"),"：",Math.round(e._matched_material.length),"*",Math.round(e._matched_material.width),"*",Math.round(e._matched_material.height)]}),(0,r.jsxs)("div",{className:"size",children:[a("素材ID"),"：",e._matched_material.modelId]})]})]})}),(0,r.jsx)("div",{className:l.divider,children:(0,r.jsx)("div",{children:["衣柜","玄关柜","餐边柜"].some((function(n){var t;return null==e||null===(t=e.sub_category)||void 0===t?void 0:t.includes(n)}))&&!o.userStore.aihouse&&"C00002170"!==o.userStore.userInfo.tenantId&&(0,r.jsx)(vt.A,{style:{marginLeft:10},type:"primary",size:"small",onClick:function(){b.current.onModal()},children:a("AI搭柜")})})}),(0,r.jsxs)("div",{className:l.tabContainer,children:[(0,r.jsx)("div",{className:"".concat(l.tabItem," ").concat("套系素材"===f?l.active:""),onClick:function(){return p("套系素材")},children:a("套系素材")}),(0,r.jsx)("div",{className:"".concat(l.tabItem," ").concat("云素材"===f?l.active:""),onClick:function(){return p("云素材")},children:a("云素材")})]}),(0,r.jsx)("div",{className:l.goodsInfo,children:x?(0,r.jsxs)("div",{className:l.loading,children:[(0,r.jsx)(yt.A,{size:"large"})," "]}):s&&s.length>0?s.map((function(n,t){return(0,r.jsxs)("div",{className:l.goodsItem,onClick:function(){return Ct((function(){var r,i,a,l,c,s,u,d,p;return Mt(this,(function(h){switch(h.label){case 0:return e.locked?[2]:(o.designStore.setSelectedIndex(t),"套系素材"!==f?[3,1]:(N.nb.DispatchEvent(N.n0.ReplaceMaterial,n),[3,4]));case 1:return a=null,[4,(0,wt.Y2)({materialIds:null==n?void 0:n.materialId})];case 2:return(null==(l=h.sent())||null===(i=l.result)||void 0===i||null===(r=i.result)||void 0===r?void 0:r[0])&&(a=null==l||null===(s=l.result)||void 0===s||null===(c=s.result)||void 0===c?void 0:c[0]),[4,(0,jt.h)(n.materialId)];case 3:u=h.sent(),a&&(d={modelId:a.MaterialId,imageUrl:n.imageUrl.startsWith("https://")?n.imageUrl:L.L4+n.imageUrl,name:a.MaterialName,originalLength:a.PICLength,originalWidth:a.PICWidth,originalHeight:a.PICHeight,length:a.PICLength,width:a.PICWidth,height:a.PICHeight,modelLoc:e.modelLoc,modelFlag:a.ModelFlag.toString(),topViewImage:u,figureElement:e},p=function(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},r=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(t).filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})))),r.forEach((function(e){kt(n,e,t[e])}))}return n}({},n,d),N.nb.DispatchEvent(N.n0.ReplaceMaterial,p)),h.label=4;case 4:return[2]}}))}))()},children:[t===o.designStore.selectedIndex&&(0,r.jsx)("div",{className:l.selectIcon}),(0,r.jsx)("img",{src:n.imageUrl,alt:""}),(0,r.jsx)("div",{className:l.sizeInfo,children:n.name}),(null==n?void 0:n.length)?(0,r.jsx)("div",{className:l.sizeInfo,style:{color:"rgba(255, 255, 255, 0.6)"},children:Math.round(null==n?void 0:n.length)+"*"+Math.round(null==n?void 0:n.width)+"*"+Math.round(null==n?void 0:n.height)}):null]},t)})):(0,r.jsxs)("div",{className:l.noData,children:[(0,r.jsx)("img",{className:"emptyImg",src:t(78793),alt:""}),(0,r.jsx)("span",{children:a("暂无可用素材")})]})}),(0,r.jsx)(St.A,{onParams:function(){},selectedFigureElement:e,ref:b})]})})})),Nt=t(46396),Dt=t(65640);function Et(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function Pt(n,e,t,r,i,o,a){try{var l=n[o](a),c=l.value}catch(n){return void t(n)}l.done?e(c):Promise.resolve(c).then(r,i)}function Ft(n,e){return function(n){if(Array.isArray(n))return n}(n)||function(n,e){var t=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=t){var r,i,o=[],a=!0,l=!1;try{for(t=t.call(n);!(a=(r=t.next()).done)&&(o.push(r.value),!e||o.length!==e);a=!0);}catch(n){l=!0,i=n}finally{try{a||null==t.return||t.return()}finally{if(l)throw i}}return o}}(n,e)||Ot(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Bt(n){return function(n){if(Array.isArray(n))return Et(n)}(n)||function(n){if("undefined"!=typeof Symbol&&null!=n[Symbol.iterator]||null!=n["@@iterator"])return Array.from(n)}(n)||Ot(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ot(n,e){if(n){if("string"==typeof n)return Et(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(t):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Et(n,e):void 0}}function Rt(n,e){var t,r,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(l){return function(c){return function(l){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(o=0)),o;)try{if(t=1,r&&(i=2&l[0]?r.return:l[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,l[1])).done)return i;switch(r=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==l[0]&&2!==l[0])){o=0;continue}if(3===l[0]&&(!i||l[1]>i[0]&&l[1]<i[3])){o.label=l[1];break}if(6===l[0]&&o.label<i[1]){o.label=i[1],i=l;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(l);break}i[2]&&o.ops.pop(),o.trys.pop();continue}l=e.call(n,o)}catch(n){l=[6,n],r=0}finally{t=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,c])}}}var Tt={popupType:"Layout",sceneMode:"2D",prev3DSceneMode:"3D_FirstPerson"},Lt=(0,M.observer)((function(n){n.updateKey;var e=(0,i.B)().t,t=I().styles,o=((0,q.Zp)(),(0,O.P)()),a=o.homeStore,l=a.viewMode,c=(a.drawPictureMode,a.roomEntities),s=a.isdrawPicture,u=(a.guideMapCurrentRoom,a.setShowDreamerPopup,a.setIsdrawPicture),d=(a.setDrawPictureMode,a.setViewMode),f=a.setSelectEntity,p=a.setShowReplace,h=a.setShowSaveLayoutSchemeDialog,x=Ft((0,z.useState)(!1),2),m=x[0],b=x[1],g=Ft((0,z.useState)(!1),2),v=g[0],y=g[1],w=Ft((0,z.useState)(l),2),j=w[0],S=w[1],_=Ft((0,z.useState)(Tt.popupType),2),A=_[0],C=_[1],k=Ft((0,z.useState)(W.qB.Figure2D),2),M=k[0],K=k[1],Y=Ft((0,z.useState)(null),2),Z=Y[0],J=Y[1],Q=Ft((0,z.useState)([]),2),nn=Q[0],en=Q[1],tn=Ft((0,z.useState)([{value:"Layout",label:e("推荐布局")},{value:"material",label:e("编辑布局")}]),2),rn=tn[0],on=tn[1],ln="PadLeftPanel",cn=N.nb.instance,un=cn.layout_container,dn=(cn.scene3D,Ft((0,z.useState)(!1),2)),fn=dn[0],pn=dn[1],hn=window.innerWidth<=460,xn={attribute:[{value:"attribute",label:e("属性")}],sizeEditor:[{value:"sizeEditor",label:e("尺寸")}],SpaceAreaAttribute:[{value:"SpaceAreaAttribute",label:e("属性")}]},mn=[{value:W.qB.Figure2D,label:e(hn?"布局":"布局模式")},{value:W.qB.Texture,label:e(hn?"风格":"风格模式")}],bn=(e("漫游"),e("鸟瞰"),function(){var n,t,r=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(cn.layout_container)if(xn[A])rn.length=0,(n=rn).push.apply(n,Bt(xn[A]));else if("2D"===j){var i,o;if(Dt.log(cn.layout_container.drawing_figure_mode),cn.layout_container.drawing_figure_mode==W.qB.Figure2D)rn.length=0,(i=rn).push.apply(i,[{value:"Layout",label:e("推荐布局")},{value:"material",label:e("编辑布局")}]);else rn.length=0,(o=rn).push.apply(o,[{value:"Matching",label:e("推荐风格")}])}else{var a;rn.length=0,(a=rn).push.apply(a,[{value:"replace",label:e("换搭素材")}])}if(!Z){var l=rn.findIndex((function(n){return"replace"===n.value}));l>=0&&rn.splice(l,1)}r&&(rn.find((function(n){return n.value===A}))||wn((null===(t=rn[0])||void 0===t?void 0:t.value)||""));on(Bt(rn))}),vn=function(){if("2D"===j){var n=[{label:e("下一步"),onClick:function(){return(n=function(){return Rt(this,(function(n){switch(n.label){case 0:return[4,Cn.y.instance.autoApplySeries()];case 1:return n.sent(),S(Tt.prev3DSceneMode),[2]}}))},function(){var e=this,t=arguments;return new Promise((function(r,i){var o=n.apply(e,t);function a(n){Pt(o,r,i,a,l,"next",n)}function l(n){Pt(o,r,i,a,l,"throw",n)}a(void 0)}))})();var n}}].filter((function(n){return n}));en(n)}else if("3D"===j){var t=[{label:e("返回布局"),onClick:function(){S("2D")}}];en(t)}else if("3D_FirstPerson"===j){var r=[{label:e("上一步"),onClick:function(){S("2D")}}];en(r)}s&&yn()},yn=function(){en([])};(0,z.useEffect)((function(){vn()}),[j,s]);var wn=function(n){Tt.popupType=n,C(n)};(0,z.useEffect)((function(){fn&&(Dt.log("清除灯光效果"),ce.Y.cleanLight(),pn(!1))}),[fn]);var jn=function(n,e){n&&n===Z||(un.drawing_figure_mode===W.qB.Figure2D&&"2D"===j?"Furniture"===e?"Layout"===Tt.popupType&&wn("material"):wn("Layout"):"Furniture"===e?n&&wn("replace"):n&&"replace"==Tt.popupType?wn("replace"):wn("Matching"),J(n))};(0,z.useEffect)((function(){return bn(),vn(),N.nb.on_M(U.U.FigureElementSelected,ln,(function(n){jn(n,"Furniture")})),N.nb.on_M(U.U.SelectingTarget,ln,(function(n,e,t){if("2D"==j){var r=n||null;if(null==r?void 0:r.figure_element)jn(r.figure_element,"Furniture");else if(r){var i=r._room;(null==i?void 0:i.tile)?jn(i.tile,"RoomArea"):jn(null,"RoomArea")}else jn(null,"RoomArea");f(r),r||p(!1);var o=r;if(o&&(null==o?void 0:o._room))Cn.y.instance.current_rooms=[o._room],Cn.y.instance.emitSeriesSamplesWithOrdering({clickOnRoom:!0});else if(un._rooms){var a=un._rooms.filter((function(n){return n&&n.furnitureList.length>0}));Cn.y.instance.current_rooms=a,Cn.y.instance.emitSeriesSamplesWithOrdering({clickOnRoom:null})}}})),N.nb.on_M(sn.$.showPopup,ln,(function(n){wn(n)})),N.nb.on_M(U.U.SceneModeChanged,ln,(function(n){S(n)})),function(){N.nb.off_M_All({object_id:ln})}}),[]),(0,z.useEffect)((function(){"2D"===l&&S("2D")}),[l]),(0,z.useEffect)((function(){!function(n){var e=N.nb.instance.layout_container,t=N.nb.instance.scene3D;if("2D"===n)t&&t.stopRender(),window.innerWidth<.8*window.innerHeight?(kn.f.focusCenterByWholeBox(e,.7),N.nb.instance.update()):(kn.f.focusCenterByWholeBox(e,.6),N.nb.instance.update()),N.nb.emit_M(V.z.showLight3DViewer,!1),d("2D");else if("3D"===n)N.nb.DispatchEvent(N.n0.Match3dPreviewMaterials,null),N.nb.emit_M(V.z.showLight3DViewer,!0),t.setCemeraMode(In.I5.Perspective),d("3D"),N.nb.DispatchEvent(N.n0.cleanSelect,null),t&&(t.startRender(),N.nb.emit_M(U.U.Scene3DUpdated,!1));else if("3D_FirstPerson"===n){if(N.nb.DispatchEvent(N.n0.Match3dPreviewMaterials,null),N.nb.emit_M(V.z.showLight3DViewer,!0),t.setCemeraMode(In.I5.FirstPerson),"2D"==l){var r=e._room_entities.reduce((function(n,e){return n?e._area>n._area?e:n:e}),null);if(r){var i,a=N.nb.instance.layout_container;a?(0==r._view_cameras.length&&se.q.updateViewCameraEntities(a,null,{methods:2}),t.active_controls.bindViewEntity(r._view_cameras[0]),o.homeStore.setCurrentViewCameras(r._view_cameras)):t.setCenter((null==r||null===(i=r._main_rect)||void 0===i?void 0:i.rect_center)||new H.Pq0(0,0,0)),t.update()}else{var s,u;t.setCenter((null===(u=c[0])||void 0===u||null===(s=u._main_rect)||void 0===s?void 0:s.rect_center)||new H.Pq0(0,0,0))}}d("3D_FirstPerson"),t&&(t.startRender(),N.nb.emit_M(U.U.Scene3DUpdated,!1))}n&&"2D"!==n&&(Tt.prev3DSceneMode=n),Tt.sceneMode=n,bn(!0)}(j)}),[j]),(0,z.useEffect)((function(){u(!1),pn(!0)}),[l]);var Sn=!m;window.innerWidth,window.innerHeight;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:t.navigation+" topNavigation",children:[L.um?"true":"false",!o.homeStore.isdrawPicture&&(0,r.jsxs)("div",{className:"".concat(t.backBtn," ").concat("2D"!==j?t.blackColor:""),onClick:function(){!un._layout_scheme_id&&un._room_entities.length>0?h({show:!0,source:"exitBtn"}):un._layout_scheme_id?(N.nb.DispatchEvent(N.n0.SaveLayoutScheme,null),o.homeStore.setIsAutoExit("autoExit")):(window.parent.postMessage({origin:"layoutai.api",type:"canClose",data:{canClose:!0}},"*"),X.K.exitSDK(),window.location.href=L.O9)},children:[(0,r.jsx)(Ce.A,{style:{fontSize:"16px",marginRight:"4px",color:"2D"!==o.homeStore.viewMode?"#fff":"#595959"},iconClass:"icon-a-fangxiangzuo"}),e("退出")]}),v&&(0,r.jsx)("div",{className:"",style:{top:50,right:12,position:"fixed",zIndex:"999",background:"#fff",padding:"10px"},children:(0,r.jsx)(An.A,{onClose:function(){y(!1)}})})]}),"2D"===j&&(0,r.jsx)("div",{className:t.topTabs+" topTabs",children:(0,r.jsx)(T.A,{value:M,onChange:function(n){var e;e=~~n,cn.layout_container,e!==M&&(cn.layout_container.drawing_figure_mode=e,e===W.qB.Texture&&Cn.y.instance.autoApplySeries(),cn.update(),K(e),bn(!0))},block:!0,options:mn})}),s&&(0,r.jsx)(rt,{}),(0,r.jsx)("div",{children:(0,r.jsx)($.A,{rightOffset:12,topOffset:20})}),(0,r.jsx)("div",{className:t.sideToolbarContainer+" sideToolbar "+("2D"!=j?"is_3d_mode":""),children:(0,r.jsx)(G.A,{setSceneMode:S})}),"2D"==j&&(0,r.jsxs)("div",{id:"pad_left_panel",className:t.leftPanelRoot+" leftPanelRoot "+(Sn?"":"panel_hide"),children:[Sn&&(0,r.jsx)("div",{className:"closeBtn iconfont iconclose1",onClick:function(){return b(!0)}}),Sn&&rn.length>1&&(0,r.jsx)("div",{className:t.tabBar,children:(0,r.jsx)(T.A,{value:A,onChange:function(n){wn(n)},block:!0,options:rn})}),(0,r.jsxs)("div",{className:t.popupContainer+" side_pannel",children:[(0,r.jsx)("div",{className:t.listContainer,style:{display:"Layout"===A?"block":"none"},children:(0,r.jsx)(D.A,{width:400,showSchemeName:!1,isLightMobile:!0})}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"Matching"===A?"block":"none"},children:[" ",(0,r.jsx)(E.A,{})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"material"===A?"block":"none"},children:[" ",(0,r.jsx)(P.A,{})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"attribute"===A?"block":"none"},children:[" ",(0,r.jsx)(F.A,{})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"replace"===A?"block":"none"},children:[" ",(0,r.jsx)(B.A,{selectedFigureElement:Z})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"searchMaterial"===A?"block":"none"},children:[" ",(0,r.jsx)(R.A,{})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"sizeEditor"===A?"block":"none"},children:[" ",(0,r.jsx)(_n,{})," "]}),(0,r.jsxs)("div",{className:t.listContainer,style:{display:"SpaceAreaAttribute"===A?"block":"none"},children:[" ",(0,r.jsx)(Qn,{})," "]})]})]}),rn.length>0&&"2D"==j&&(0,r.jsx)("div",{className:t.collapseBtn+(Sn?" iconfont iconfill_left":" panel_hide iconfont iconfill_right"),onClick:function(){b(!m)}}),(0,r.jsx)(ke.A,{in:"2D"!=l&&!s&&Z,timeout:300,classNames:{enter:"fadeEnter",enterActive:"fadeEnterActive",exit:"fadeExit",exitActive:"fadeExitActive"},unmountOnExit:!0,children:(0,r.jsx)("div",{id:"pad_left_panel",className:"".concat(t.leftPanelRoot," ").concat("2D"!=l?t.materialReplace:""),children:(0,r.jsx)(zt,{selectedFigureElement:Z})})}),(0,r.jsx)(gn,{}),s&&(0,r.jsx)(Ve,{}),(0,r.jsxs)("div",{className:t.bottomButtons+" bottomBtns"+(Sn?" showLeftPanel":""),children:[nn&&nn.length>0&&nn.map((function(n,e){return(0,r.jsx)("div",{className:"btn"+("2D"!==j?" blackColor":""),onClick:null==n?void 0:n.onClick,children:null==n?void 0:n.label},"bottomBtn_"+e)})),(0,r.jsx)(Nt.If,{condition:"3D_FirstPerson"==l&&!o.homeStore.isdrawPicture,children:(0,r.jsx)(Be,{})})]}),(0,r.jsx)(an,{}),s&&(0,r.jsx)(le,{})]})}))}}]);